#!/usr/bin/env node

const { execSync } = require('child_process');
const inquirer = require('inquirer');
inquirer.registerPrompt(
  'checkbox-plus',
  require('inquirer-checkbox-plus-prompt'),
);

const lastMonthDate = new Date(new Date().setDate(0));
const month = lastMonthDate.getMonth() + 1;
const year = lastMonthDate.getYear() - 100;
const date = `${month < 10 ? '0' : ''}${month}.${year}`;

let author = '';
let userDate = '';
let since = '';
let until = '';
let dir = '';

async function main() {
  const answer = await inquirer.prompt([
    {
      name: 'author',
      type: 'input',
      message: 'Please enter email:',
      default: execSync('git config user.email').toString().trim(),
    },
    {
      name: 'date',
      type: 'input',
      message:
        'Please enter a month or date range (08.21 or 15.08.21-01.09.21):',
      default: date,
    },
  ]);

  author = answer.author.toLowerCase();
  userDate = answer.date.toLowerCase();

  if (userDate.split('.').length > 2) {
    const [from, to] = userDate.split('-');
    since = from.trim();
    until = to.trim();
  } else {
    const [m, y] = userDate.split('.');
    const lastDate = new Date((y.length === 2 ? '20' : '') + y, m, 1);
    const [lm, ly] = [lastDate.getMonth() + 1, lastDate.getYear() - 100];

    since = `01.${m}.${y}`;
    until = `01.${lm}.${ly}`;
  }

  const answerDir = await inquirer.prompt([
    {
      name: 'dir',
      type: 'input',
      message: 'Please enter directory:',
      default: `../archives/${userDate}/yield-archive`,
    },
  ]);

  dir = answerDir.dir;

  const mkdir = `mkdir -p ${dir}`;
  const cmd = `git log --author='${author}' --since='${since}' --until='${until}' --format="%H" --reverse | sed 's/$/^!/g' | nl -w 4 -s ' ' | xargs -I{} sh -c "git format-patch --start-number {} --output-directory='${dir}'"`;

  console.log('');
  console.log(`Creating directory ${dir} ...`);
  console.log(`> ${mkdir}`);
  execSync(mkdir);

  console.log('');
  console.log(`Generating files ...`);
  console.log(`> ${cmd}`);
  execSync(cmd);

  console.log('');
  console.log('DONE!');
}

main();
