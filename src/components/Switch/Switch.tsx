import React, { <PERSON> } from 'react';

import cx from 'classnames';

import classNames from './Switch.module.css';

export type Props = {
  id?: string;
  value: boolean;
  onChange: (newValue: boolean) => void;
  disabled?: boolean;
};

const Switch: FC<Props> = ({ id, value, onChange, disabled = false }) => {
  const handleToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    !disabled && onChange(!value);
  };

  return (
    <label
      htmlFor={id}
      className={cx(classNames.switch, disabled && classNames.disabled)}
    >
      <input
        id={id}
        type="checkbox"
        checked={value}
        onChange={handleToggle}
        disabled={disabled}
      />
      <span
        className={cx(
          classNames.toggle,
          value ? classNames.on : classNames.off,
          disabled && classNames.disabled,
        )}
      >
        <span className={classNames.circle} />
      </span>
    </label>
  );
};

export default Switch;
