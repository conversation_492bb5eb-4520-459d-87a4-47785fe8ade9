.switch {
  display: inline-block;
  position: relative;
  min-width: 28px;
  height: 16px;
  margin-left: 10px;
  cursor: pointer;
}

.switch input {
  visibility: hidden;
}

.disabled {
  cursor: not-allowed;
}

.toggle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ccc;
  border-radius: 17px;
  transition: background-color 0.3s;
}

.toggle.off {
  background-color: var(--color-grey-50);
}

.toggle.on {
  background-color: var(--color-grey-80);
}

.toggle.disabled {
  background-color: var(--color-grey-100);
}

.circle {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--color-white);
  transition: transform 0.3s;
}

.toggle.on .circle {
  transform: translateX(12px);
}
