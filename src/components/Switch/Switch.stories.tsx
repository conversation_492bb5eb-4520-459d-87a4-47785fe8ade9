import React, { useState } from 'react';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

import SwitchExample, { Props } from './Switch';

export default {
  title: 'Components/Switch',
  component: SwitchExample,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  display: flex;
  margin-left: auto;
  margin-right: auto;
  margin-top: 50px;
`;

export const Default: Story<Props> = () => {
  const [isToggled, setIsToggled] = useState(false);

  return (
    <Wrapper>
      <SwitchExample
        id="switch_test_storybook"
        value={isToggled}
        onChange={() => setIsToggled(!isToggled)}
      />
    </Wrapper>
  );
};

export const Disabled: Story<Props> = () => {
  const [isToggled, setIsToggled] = useState(false);

  return (
    <Wrapper>
      <SwitchExample
        id="switch_test_storybook__disabled"
        disabled
        value={isToggled}
        onChange={() => setIsToggled(!isToggled)}
      />
    </Wrapper>
  );
};
