import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useStore } from 'effector-react';
import { feature, featureCollection } from '@turf/helpers';

import Loader from 'components/Loader/Loader';

import config from 'config';
import GeometryRenderer from 'features/analysis/GeometryRenderer';
import { isSidebarExpanded$ } from 'features/field-list/models/field-list';
import { getFullRasterUrl } from 'features/raster-layer/utils';
import { FieldSeason, FillingLayers } from 'models/fieldSeasons';
import { scaleBbox } from 'utils/map';
import { calculateBboxScales } from './utils';

import classNames from './MapFieldPreviewWidget.module.css';

type Props = {
  fieldSeason: FieldSeason;
  layer: FillingLayers;
  isPlanet?: boolean;
  url?: string;
  showLoader?: boolean;
};

const widgetPadding = 72;
const extendPercent = 40;

const MapFieldWidgetImage = ({
  layer,
  fieldSeason,
  isPlanet,
  url,
  showLoader,
}: Props) => {
  const ref = useRef<HTMLDivElement | null>(null);
  const isSidebarExpanded = useStore(isSidebarExpanded$);
  const [loading, setLoading] = useState(true);
  const [size, setSize] = useState({ width: 0, height: 0 });

  const updateSize = () => {
    // hack for rendering, todo: need to investigate and make better
    setTimeout(() => {
      if (ref.current) {
        setSize({
          width: ref.current.offsetWidth,
          height: ref.current.offsetHeight,
        });
      }
    }, 0);
  };

  useEffect(() => {
    updateSize();
  }, []);

  useEffect(() => {
    updateSize();
  }, [isSidebarExpanded]);

  const isRgbLayer = layer === FillingLayers.SatelliteImage;
  const needExtend = isRgbLayer && !isPlanet;
  const imageUrl = getFullRasterUrl({
    url,
    extendPercent: needExtend ? extendPercent : undefined,
    noImagePrefix: [
      FillingLayers.Ndvi,
      FillingLayers.ContrastedNdvi,
      FillingLayers.SatelliteImage,
    ].includes(layer),
    isContrasted: [
      FillingLayers.ContrastedNdvi,
      FillingLayers.ElevationMap,
      FillingLayers.BrightnessMap,
      FillingLayers.ProductivityMap,
    ].includes(layer),
  });

  useEffect(() => {
    if (!imageUrl || layer !== FillingLayers.Ndvi) {
      return;
    }
    setLoading(true);

    const img = new Image();
    img.src = imageUrl;

    img.onload = () => {
      setLoading(false);
    };

    img.onerror = () => {
      setLoading(false); // Handle the error state if needed
    };
  }, [imageUrl, layer]);

  const rgbBbox =
    fieldSeason.bbox && needExtend
      ? scaleBbox(fieldSeason.bbox, extendPercent)
      : null;
  const rgbScales =
    fieldSeason.bbox && rgbBbox && needExtend
      ? calculateBboxScales(fieldSeason.bbox, rgbBbox)
      : null;
  let backgroundWidth = 'auto';
  let backgroundHeight = 'auto';
  if (fieldSeason.bbox) {
    const width =
      (size.width - widgetPadding * 2) / (rgbScales?.widthScale || 1);
    const height =
      (size.height - widgetPadding * 2) / (rgbScales?.heightScale || 1);
    if (height > width) {
      backgroundWidth = `${width}px`;
    } else {
      backgroundHeight = `${height}px`;
    }
  }

  const staticImageSrc = useMemo(() => {
    if (!size.width || !size.height) {
      return '';
    }
    return `https://api.mapbox.com/styles/v1/mapbox/satellite-streets-v12/static/[${fieldSeason.bbox}]/${size.width}x${size.height}@2x?attribution=false&logo=false&padding=${widgetPadding}&access_token=${config.apiKeys.mapbox}`;
  }, [fieldSeason.bbox, size]);

  return (
    <div
      ref={ref}
      className={classNames.staticImageContainer}
      style={{ backgroundImage: `url(${staticImageSrc})` }}
    >
      {needExtend && (
        <>
          {imageUrl && (
            <div
              className={
                isRgbLayer
                  ? classNames.rgbImageContainer
                  : classNames.ndviImageContainer
              }
              style={{
                backgroundImage: `url(${imageUrl})`,
                backgroundSize: `${backgroundWidth} ${backgroundHeight}`,
              }}
            />
          )}
          {showLoader && <Loader size={32} color="var(--color-grey-70)" />}
        </>
      )}

      {layer === FillingLayers.Ndvi && loading && (
        <Loader size={32} color="var(--color-grey-70)" />
      )}
      <GeometryRenderer
        className={classNames.zones}
        containerStyle={{
          width: size.width - widgetPadding * 2,
          height: size.height - widgetPadding * 2,
        }}
        styles={() => ({
          stroke:
            layer === FillingLayers.NoMap
              ? 'var(--color-white)'
              : showLoader
              ? 'var(--color-black-light)'
              : 'var(--color-black)',
          fill:
            layer === FillingLayers.NoMap
              ? 'rgba(255,255, 255, 0.35)'
              : undefined,
          weight: 1,
        })}
        geojson={
          fieldSeason.geom
            ? featureCollection([
                feature(
                  fieldSeason.geom,
                  !needExtend && imageUrl
                    ? {
                        type: 'image',
                        imageClassName: classNames.pixelated,
                        imageUrl,
                      }
                    : {},
                ),
              ])
            : featureCollection([])
        }
      />
    </div>
  );
};

export default MapFieldWidgetImage;
