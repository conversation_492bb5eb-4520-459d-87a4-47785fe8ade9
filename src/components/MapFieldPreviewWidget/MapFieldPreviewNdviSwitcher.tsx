import IconButton from 'components/IconButton/IconButton';

import classNames from './MapFieldPreviewNdviSwitcher.module.css';

type Props = {
  changeDate: (direction: number) => void;
  disabled?: boolean;
  disabledRight: boolean;
  disabledLeft: boolean;
};

const MapFieldPreviewNdviSwitcher = ({
  disabled,
  changeDate,
  disabledRight,
  disabledLeft,
}: Props) => {
  return (
    <div className={classNames.wrapper}>
      <IconButton
        className={classNames.arrowButton}
        iconSize={16}
        icon="ChevronLeft"
        color="var(--color-white)"
        hoverColor="var(--color-white)"
        disabled={disabledLeft || disabled}
        onClick={() => changeDate(-1)}
      />
      <IconButton
        className={classNames.arrowButton}
        iconSize={16}
        icon="ChevronRight"
        color={'var(--color-white)'}
        hoverColor="var(--color-white)"
        disabled={disabledRight || disabled}
        onClick={() => changeDate(1)}
      />
    </div>
  );
};

export default MapFieldPreviewNdviSwitcher;
