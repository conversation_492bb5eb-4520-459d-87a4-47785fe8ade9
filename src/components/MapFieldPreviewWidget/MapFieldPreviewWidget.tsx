import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useStore, useStoreMap } from 'effector-react';
import { differenceInHours, differenceInMonths, parseISO } from 'date-fns';
import { debounce } from 'lodash';

import {
  FieldBaseLayer,
  FieldSeason,
  fieldSeasonsLastDateUpdates$,
  FillingLayers,
  fetchFieldSeasonLastCleanDatesFx,
  fieldsLastCleanDateTaskBySeason$,
  FieldLastCleanDate,
  isPlanetSupportedLayer,
} from 'models/fieldSeasons';
import {
  getAvailableDateImageByIdOrDate,
  useRasterLayerUrl,
  useTiffData,
} from 'features/raster-layer/RasterLegend/utils';
import { calendarDateChanged } from 'models/availableDates/calendars';
import { availableDatesByField$ } from 'models/availableDates';
import { eventLogged } from 'models/analytics';
import { formatDate } from 'utils/formatters';
import { currentSeason$ } from 'models/seasons';

import Button from 'components/Button/Button';
import PositionTo from 'components/PositionTo/PositionTo';
import { ndviMetaByField$ } from 'features/vra/models/ndvi';
import MapFieldPreviewNdviSwitcher from './MapFieldPreviewNdviSwitcher';
import RasterLegend from 'features/raster-layer/RasterLegend/RasterLegend';
import MapFieldWidgetImage from './MapFieldWidgetImage';
import SvgIcon from '../SvgIcon';
import { Text } from '../Text';

import classNames from './MapFieldPreviewWidget.module.css';
import { useProcessedPlanetMeta } from '../../utils/use-processed-planet-meta';

type Props = {
  fieldSeason: FieldSeason;
  layer: FieldBaseLayer;
  isPlanet: boolean;
  onFullScreen?: (layer: FieldBaseLayer) => void;
  showLoader?: boolean;
  customUrl?: string;
};

const ndviInstanceId = 'map-field-instance-0';

const isDateOld = (lastCleanDate: { date: string }) =>
  differenceInMonths(new Date(), parseISO(lastCleanDate.date)) >= 1;
const isUpdateFresh = (updateAt: string) =>
  differenceInHours(new Date(), parseISO(updateAt)) < 8;

const MapFieldPreviewWidget: FC<Props> = ({
  fieldSeason,
  layer,
  isPlanet,
  onFullScreen,
  showLoader,
  customUrl,
}) => {
  const { t, i18n } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [lastCleanDateInfo, setLastCleanDateInfo] = useState<{
    date: FieldLastCleanDate | null;
    isNew: boolean;
  }>({ date: fieldSeason.last_clean_date || null, isNew: false });
  const [cloudNDVIWarning, setCloudNDVIWarning] = useState(false);
  const isFieldUpdating = useStoreMap(fieldSeasonsLastDateUpdates$, (items) =>
    items.includes(fieldSeason.uuid),
  );
  const isSeasonUpdating = useStore(fetchFieldSeasonLastCleanDatesFx.pending);
  const currentSeason = useStore(currentSeason$);
  const seasonTaskProcessing = useStoreMap(
    fieldsLastCleanDateTaskBySeason$,
    (tasksBySeason) => {
      const seasonTask = currentSeason
        ? tasksBySeason[currentSeason.uuid]
        : null;
      return !!seasonTask && seasonTask.status === 'processing';
    },
  );
  const isUpdating =
    isFieldUpdating || isSeasonUpdating || seasonTaskProcessing;

  const isLayerAvailable =
    (isPlanet && isPlanetSupportedLayer(layer)) || !isPlanet;
  const planetMeta = useProcessedPlanetMeta(fieldSeason.uuid);
  const planetMetaDates = planetMeta.map((meta) => ({
    date: meta.date,
    id: meta.uuid,
  }));

  const ndviMeta = useStoreMap(
    ndviMetaByField$,
    (items) => items[fieldSeason.uuid] || [],
  );

  const fieldAvailableDates = useStoreMap(
    availableDatesByField$,
    (items) => items[fieldSeason.uuid] || [],
  );
  const availableDates = isPlanet ? planetMetaDates : fieldAvailableDates;
  const lastAvailableDate = availableDates[availableDates.length - 1]?.date;
  const fieldLastCleanDate = fieldSeason.last_clean_date;

  useEffect(() => {
    if (
      !showLoader &&
      !lastCleanDateInfo.isNew &&
      !!fieldSeason.last_clean_date &&
      fieldSeason.last_clean_date.date !== lastCleanDateInfo.date?.date &&
      !isDateOld(fieldSeason.last_clean_date)
    ) {
      setLastCleanDateInfo({
        date: fieldSeason.last_clean_date || null,
        isNew: true,
      });
    }
  }, [fieldSeason.last_clean_date, lastCleanDateInfo, showLoader]);

  useEffect(() => {
    let newIndex = -1;
    if (layer === FillingLayers.Ndvi && fieldLastCleanDate?.date) {
      const isLastCleanDateOld = isDateOld(fieldLastCleanDate);
      newIndex = isLastCleanDateOld
        ? availableDates.length - 1
        : availableDates.findIndex(
            (date) => date.date === fieldLastCleanDate?.date,
          );
      if (isLastCleanDateOld && isUpdateFresh(fieldLastCleanDate.updated_at)) {
        setCloudNDVIWarning(true);
      }
    }
    if (currentIndex === -1 && newIndex === -1) {
      newIndex = availableDates.length - 1;
    }
    if (newIndex !== -1 && currentIndex === -1) {
      setCurrentIndex(newIndex);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [availableDates]);

  useEffect(() => {
    const calendarDate = availableDates[availableDates.length - 1]?.date;
    if (calendarDate) {
      setCurrentIndex(
        availableDates.findIndex((date) => date.date === calendarDate),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPlanet]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedCalendarDateChanged = useCallback(
    debounce((index) => {
      calendarDateChanged({
        calendarId: ndviInstanceId,
        date: availableDates[index]?.date || '',
        fieldSeason,
      });
    }, 500),
    [availableDates, fieldSeason],
  );

  const changeDate = (direction: number) => {
    setCurrentIndex((prevIndex) => {
      let newIndex = prevIndex + direction;

      if (newIndex < 0) {
        newIndex = 0;
      } else if (newIndex >= availableDates.length) {
        newIndex = availableDates.length - 1;
      }

      debouncedCalendarDateChanged(newIndex);

      return newIndex;
    });
    if (cloudNDVIWarning) {
      setCloudNDVIWarning(() => false);
    }
  };

  const selectedAvailableDate = useMemo(() => {
    if (showLoader) {
      return null;
    }
    return currentIndex === -1
      ? lastCleanDateInfo.date?.date
      : availableDates[currentIndex]?.date || lastAvailableDate || null;
  }, [
    lastCleanDateInfo.date,
    lastAvailableDate,
    currentIndex,
    availableDates,
    showLoader,
  ]);

  const isNDVILayer =
    layer === FillingLayers.Ndvi || layer === FillingLayers.ContrastedNdvi;
  const isRgbLayer = layer === FillingLayers.SatelliteImage;
  const noMap = layer === FillingLayers.NoMap;

  const selectedNdviMeta = ndviMeta.find(
    (meta) => meta.date === selectedAvailableDate,
  );
  let url: string | undefined;
  const availableDateUrl = useRasterLayerUrl({
    fieldSeason,
    layer,
    date: selectedAvailableDate,
    isPlanet: isPlanet,
  });
  if (
    (selectedNdviMeta || (isPlanet && selectedAvailableDate)) &&
    (isNDVILayer || isRgbLayer)
  ) {
    url = getAvailableDateImageByIdOrDate(
      fieldSeason.uuid,
      isPlanet ? selectedAvailableDate! : selectedNdviMeta!.date,
      isNDVILayer ? FillingLayers.Ndvi : layer,
      isPlanet,
      fieldSeason.geom_updated_at,
    );
  } else {
    url = availableDateUrl;
  }
  url = customUrl || url;
  const tiffData = useTiffData(url);

  let title = !noMap
    ? t(`field-preview-widget.title.${layer}`)
    : t('field-preview-widget.title.productivity-map');

  return (
    <div key={fieldSeason.uuid} className={classNames.container}>
      <MapFieldWidgetImage
        fieldSeason={fieldSeason}
        layer={layer}
        isPlanet={isPlanet}
        url={url}
        showLoader={showLoader}
      />
      {lastCleanDateInfo.isNew && isNDVILayer && !isPlanet && (
        <PositionTo
          className={classNames.newNDVIAvailableContainer}
          placement="right bottom"
        >
          <Button
            icon="ArrowRight"
            iconSize={16}
            iconPosition="right"
            theme="map"
            rounded
            onClick={() => {
              const newDateIndex = availableDates.findIndex(
                (ad) => ad.date === lastCleanDateInfo.date?.date,
              );
              if (newDateIndex !== -1) {
                setCurrentIndex(newDateIndex);
              } else {
                setCurrentIndex(availableDates.length - 1);
              }
              setLastCleanDateInfo({ ...lastCleanDateInfo, isNew: false });
            }}
          >
            <div className={classNames.gradientBtnText}>
              {t('field-preview-widget.new-ndvi-available')}
            </div>
          </Button>
        </PositionTo>
      )}
      {cloudNDVIWarning && !lastCleanDateInfo.isNew && (
        <PositionTo
          className={classNames.cloudWarningContainer}
          placement="center bottom"
        >
          <SvgIcon
            name="Cloud"
            color="var(--color-warning-light)"
            width={16}
            height={16}
          />
          <Text size={14}>{t('field-preview-widget.ndvi.cloud-warning')}</Text>
        </PositionTo>
      )}
      <PositionTo className={classNames.titleContainer} placement="left top">
        <>
          <div>
            <span className={classNames.title}>{title}</span>
            {!!selectedAvailableDate && (isNDVILayer || isRgbLayer) && (
              <>
                {', '}
                {formatDate(
                  i18n,
                  'field-preview-widget.title.date_format',
                  selectedAvailableDate,
                )}
              </>
            )}
          </div>
          {isUpdating && layer === FillingLayers.Ndvi && !isPlanet && (
            <Text size={12}>
              {t('field-preview-widget.title.last-date-updating')}
            </Text>
          )}
        </>
      </PositionTo>
      <PositionTo
        className={classNames.fullScreen}
        offsetY={8}
        offsetX={8}
        placement="right top"
      >
        {isNDVILayer && (
          <MapFieldPreviewNdviSwitcher
            disabled={currentIndex === -1}
            disabledLeft={currentIndex === 0}
            disabledRight={currentIndex === availableDates.length - 1}
            changeDate={changeDate}
          />
        )}

        {onFullScreen && isLayerAvailable && (
          <Button
            size="small"
            iconSize={16}
            icon="Maximize"
            iconColor="white"
            theme="icon-only"
            className={classNames.fullScreenIcon}
            fitContent
            onClick={() => {
              isNDVILayer &&
                calendarDateChanged({
                  calendarId: ndviInstanceId,
                  date: selectedAvailableDate || '',
                  fieldSeason,
                });
              onFullScreen(layer);
              eventLogged({
                name: 'yield_field_preview_open',
                params: { layer },
              });
            }}
          />
        )}
      </PositionTo>
      {!isRgbLayer && !cloudNDVIWarning && (
        <PositionTo
          placement="left bottom"
          className={classNames.legend}
          offsetX={12}
          offsetY={12}
        >
          <RasterLegend
            filling={layer}
            withClouds={isNDVILayer}
            min={tiffData?.min}
            max={tiffData?.max}
          />
        </PositionTo>
      )}
    </div>
  );
};

export default MapFieldPreviewWidget;
