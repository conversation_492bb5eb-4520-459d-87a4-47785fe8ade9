.container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--color-black-90);
  border: 1px solid var(--color-black-90);
  border-radius: 8px;
  overflow: hidden;
}

.cloudWarningContainer {
  display: flex;
  align-items: center;
  gap: 16px;
  width: calc(100% - 16px);
  padding: 8px 8px 8px 16px;
  margin-bottom: 8px;
  background-color: var(--color-black);
  color: var(--color-warning-light);
  border-radius: 8px;
}

.newNDVIAvailableContainer {
  margin: 0 16px 16px 0;
  z-index: 1;
}

.zones {
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  width: calc(100% * 10 / 18);
  height: calc(100% * 10 / 18);
}

.staticImageContainer {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  /*height: 320px;*/
  /*width: 320px;*/
  width: 100%;
  height: 100%;
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: var(--color-black-90);
}

.rgbImageContainer {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
}

.ndviImageContainer {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: var(--color-black-90);
}

.zonesImage {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
}

.titleContainer {
  margin: 16px;
  font-size: 16px;
  color: var(--color-white);
  text-shadow: 0 0 2px var(--color-black);
}

.title {
  font-weight: 500;
}

.fullScreen {
  margin: 4px;
  gap: 4px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.fullScreenIcon {
  background-color: var(--color-black);
}

.legend {
  width: 100%;
}

.pixelated {
  image-rendering: pixelated;
}

.gradientBtnText {
  background: linear-gradient(
    90deg,
    var(--color-white),
    #37d279,
    var(--color-white)
  );
  animation: gradientShift 3s infinite linear;
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  -ms-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -ms-text-fill-color: transparent;
  text-fill-color: transparent;
}

@keyframes gradientShift {
  0% {
    background-position: 200% 0%;
  }
  100% {
    background-position: -200% 0%;
  }
}
