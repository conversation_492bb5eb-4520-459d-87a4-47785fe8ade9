import { useState } from 'react';
import { <PERSON>a, Story } from '@storybook/react';

import RadioButton from './RadioButton';

export default {
  title: 'Components/RadioButton',
  component: RadioButton,
} as Meta;

const wrapperStyle = {
  margin: 20,
  display: 'flex' as const,
  flexDirection: 'column' as const,
  gap: 20,
};

export const Default: Story = () => {
  const [selected, setSelected] = useState('two');

  return (
    <div style={wrapperStyle}>
      <RadioButton
        checked={selected === 'one'}
        onSelect={() => setSelected('one')}
      />

      <RadioButton
        checked={selected === 'two'}
        onSelect={() => setSelected('two')}
      />
      <RadioButton
        disabled
        checked={selected === 'two'}
        onSelect={() => setSelected('two')}
      />
      <RadioButton
        disabled
        checked={selected === 'three'}
        onSelect={() => setSelected('three')}
      />
    </div>
  );
};
