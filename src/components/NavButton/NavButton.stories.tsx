import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';
import { action } from '@storybook/addon-actions';

import NavButton from '../NavButton/NavButton';

export default {
  title: 'Components/1 Buttons/NavButton',
  component: NavButton,
} as Meta;

const ButtonWrapper = styled.div`
  width: 300px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  gap: 5px;
`;

export const Default: Story = () => (
  <ButtonWrapper>
    <NavButton disabled onClick={action('clicked-on-disabled')}>
      Field report
    </NavButton>
    <NavButton onClick={action('clicked')}>VRA map</NavButton>
  </ButtonWrapper>
);
