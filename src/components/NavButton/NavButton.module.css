.button {
  height: 32px;
  padding: 0;
  background-color: transparent;
  display: inline-flex;
  white-space: nowrap;
  width: auto;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  gap: 4px;
  transition: opacity 0.3s;
  cursor: pointer;
  position: relative;
  border: none;
}

.button:enabled:hover {
  color: var(--color-black);
}

.button:disabled {
  opacity: 0.5;
  cursor: default;
}

.text {
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
}
