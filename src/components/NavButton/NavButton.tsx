import SvgIcon from '../SvgIcon';

import { NavButtonProps } from './types';
import classNames from './NavButton.module.css';

const NavButton = ({ children, onClick, disabled = false }: NavButtonProps) => {
  return (
    <button className={classNames.button} onClick={onClick} disabled={disabled}>
      <SvgIcon name={'ArrowBack'} width={16} height={16} />
      <span className={classNames.text}>{children}</span>
    </button>
  );
};

export default NavButton;
