import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '../Button/Button';
import { Modal, ModalButtons, ModalText, ModalTitle } from '../Modal';
import { ModalSize } from '../Modal/types';

type Props = {
  title: string;
  subtitle: ReactNode;
  onClose: () => void;
  onConfirm: () => void;
};

const ApprovalModal = ({ title, subtitle, onClose, onConfirm }: Props) => {
  const { t } = useTranslation();
  return (
    <Modal size={ModalSize.Small} open>
      <ModalTitle>{title}</ModalTitle>
      <ModalText>{subtitle}</ModalText>
      <ModalButtons>
        <Button size="normal" icon="Cross" fitContent onClick={onClose}>
          {t('approval-modal.cancel')}
        </Button>
        <Button
          size="normal"
          icon="CheckMark"
          theme="black"
          fitContent
          onClick={onConfirm}
        >
          {t('approval-modal.confirm')}
        </Button>
      </ModalButtons>
    </Modal>
  );
};

export default ApprovalModal;
