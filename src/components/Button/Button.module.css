.button {
  border: none;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  width: 100%;
  align-items: center;
  transition: opacity 0.3s, background-color 0.3s;
  cursor: pointer;
  position: relative;
}

.button:disabled {
  cursor: not-allowed;
}

/* ACTIVE */
.button:enabled:active .icon,
.button:enabled[aria-expanded='true'] .icon {
  transition: opacity 0s;
  opacity: 0.5;
}
.button:enabled:active .text,
.button:enabled[aria-expanded='true'] .text {
  transition: opacity 0s;
  opacity: 0.5;
}

/* SIZE */
.size__tiny {
  height: 30px;
  border-radius: 4px;
  padding: 0 7px;
}
.size__tiny .text {
  font-weight: normal;
}
.size__small {
  height: 32px;
  border-radius: 8px;
  padding: 0 8px;
}
.size__normal {
  height: 40px;
  border-radius: 8px;
  padding: 0 12px;
}
.size__large {
  height: 50px;
  border-radius: 8px;
  padding: 0 20px;
}

/* ROUNDED */
.button__rounded.size__tiny {
  border-radius: 15px;
}
.button__rounded.size__small {
  border-radius: 18px;
}
.button__rounded.size__normal {
  border-radius: 20px;
}
.button__rounded.size__large {
  border-radius: 25px;
}

/* ICON */
.button__icon.size__tiny {
  width: 30px;
}
.button__icon.size__small {
  width: 32px;
}
.button__icon.size__normal {
  width: 40px;
}
.button__icon.size__large {
  width: 50px;
}

/* FIT */
.button__fit {
  width: fit-content;
}

/* TEXT */
.text {
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
  padding: 0 4px;
  min-width: 0;
  transition: opacity 0.3s, visibility 0.3s;
  opacity: 1;
  visibility: visible;
}

.button:disabled .text {
  opacity: 0.5;
}

.button__loading:disabled .text {
  opacity: 0;
  visibility: hidden;
}

.size__small .text {
  font-size: 12px;
  line-height: 16px;
}
.size__large .text {
  font-size: 16px;
  line-height: 22px;
}

/* ICON */
.icon {
  transition: opacity 0.3s, visibility 0.3s;
  opacity: 1;
  visibility: visible;
}

.button:disabled .icon {
  opacity: 0.5;
}

.button__loading:disabled .icon {
  opacity: 0;
  visibility: hidden;
}

/* THEME */
.theme__default {
  background-color: var(--color-grey-30);
  color: var(--color-black);
}
.theme__default:enabled:hover,
.theme__default.button__selected {
  background-color: var(--color-grey-50);
}
.theme__default:enabled:active,
.theme__default:enabled[aria-expanded='true'] {
  background-color: var(--color-grey-50);
}

.theme__primary {
  background-color: var(--color-primary);
  color: white;
}
.theme__primary:enabled:hover,
.theme__primary.button__selected {
  background-color: var(--color-secondary);
}
.theme__primary:enabled:active,
.theme__primary:enabled[aria-expanded='true'] {
  background-color: var(--color-secondary);
}

.theme__danger {
  background-color: var(--color-orange);
  color: white;
}
.theme__danger:enabled:hover,
.theme__danger.button__selected {
  background-color: var(--color-red);
}
.theme__danger:enabled:active,
.theme__danger:enabled[aria-expanded='true'] {
  background-color: var(--color-red);
}

.theme__toast {
  background-color: transparent;
  color: white;
}
.theme__toast:enabled:hover,
.theme__toast.button__selected {
  background-color: var(--color-black-medium);
}
.theme__toast:enabled:active,
.theme__toast:enabled[aria-expanded='true'] {
  background-color: var(--color-black-medium);
}

.theme__white {
  background-color: white;
  color: var(--color-black);
}
.theme__white:enabled:hover,
.theme__white.button__selected {
  background-color: var(--color-grey-30);
}
.theme__white:enabled:active,
.theme__white:enabled[aria-expanded='true'] {
  background-color: var(--color-grey-30);
}

.theme__dark {
  background-color: var(--color-black-medium);
  color: white;
}
.theme__dark:enabled:hover,
.theme__dark.button__selected {
  background-color: var(--color-black-light);
}
.theme__dark:enabled:active,
.theme__dark:enabled[aria-expanded='true'] {
  background-color: var(--color-black-light);
}

.theme__toolbar {
  background-color: transparent;
  color: inherit;
}
.theme__toolbar:enabled:hover,
.theme__toolbar.button__selected {
  background-color: #dddfe4;
}

.theme__black {
  background-color: var(--color-black);
  color: white;
}
.theme__black:enabled:hover,
.theme__black.button__selected {
  background-color: var(--color-black-medium);
}
.theme__black:enabled:active,
.theme__black:enabled[aria-expanded='true'] {
  background-color: var(--color-black-medium);
}

.theme__black-green {
  background-color: var(--color-black);
  color: white;
}

.theme__black-green:enabled:hover,
.theme__black-green.button__selected {
  background-color: var(--color-secondary);
}

.theme__outline {
  background-color: transparent;
  color: var(--color-black);
  border: 1px solid var(--color-grey-50);
  transition: background-color 0.3s;
}
.theme__outline:enabled:hover {
  background-color: rgba(
    209,
    209,
    214,
    0.4
  ); /* --color-grey-50 #d1d1d6 with 40 opacity */
}
.theme__outline:enabled:active,
.theme__outline:enabled[aria-expanded='true'] {
  background-color: var(--color-grey-50);
}

.theme__map {
  background-color: var(--color-black);
  box-shadow: 0 2px 8px 0 #22222259;
  color: white;
}
.theme__map:enabled:hover {
  background-color: #000000e6;
}
.theme__map:enabled:active,
.theme__map:enabled[aria-expanded='true'] {
  background-color: #000000e6;
}

.theme__sidebar {
  background-color: transparent;
  color: var(--color-black);
}
.theme__sidebar:enabled:hover,
.theme__sidebar.button__selected {
  background-color: var(--color-grey-30);
}

.theme__icon-only {
  background-color: transparent;
}

/* SHADOW */
.button__shadow {
  box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

/* COUNTER */
.counter {
  margin-left: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 18px;
  min-width: 18px;
  border-radius: 4px;
  padding: 0 4px;
  background-color: var(--color-white);
  color: var(--color-black);
  font-size: 12px;
  line-height: 16px;
}
