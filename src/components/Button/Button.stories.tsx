import { styled } from '@linaria/react';
import { <PERSON>a, <PERSON> } from '@storybook/react';

import Button, { Props } from './Button';

import { IconNames } from 'constants/icons';

export default {
  title: 'Components/1 Buttons/Button',
  component: Button,
  argTypes: {
    icon: {
      options: IconNames,
      control: {
        type: 'select',
      },
    },
    iconPosition: {
      options: ['left', 'right'],
      control: {
        type: 'radio',
      },
    },
    iconSize: {
      control: {
        type: 'range',
        min: 16,
        max: 48,
        step: 2,
      },
    },

    size: {
      options: ['large', 'normal', 'small'],
      control: {
        type: 'radio',
      },
    },
  },
  args: {
    size: 'normal',
    children: 'Button',
    rounded: false,
    disabled: false,
    fitContent: false,
    loading: false,
  },
} as Meta;

const ButtonWrapper = styled.div`
  width: 200px;
  margin-top: 20px;
  margin-left: auto;
  margin-right: auto;
`;
const TwoButtonWrapper = styled.div`
  width: 410px;
  margin-top: 20px;
  margin-left: auto;
  margin-right: auto;
`;
const Gap = styled.div`
  height: 20px;
`;
const Row = styled.div`
  display: flex;
  gap: 10px;
`;

export const Default: Story<Props> = (args) => (
  <ButtonWrapper>
    <Button {...args} />
    <Gap />
    <Button {...args} disabled>
      Disabled
    </Button>
    <Gap />
    <Button {...args} loading>
      Loading
    </Button>
    <Gap />
    <Button {...args} loading disableWhenLoading={false}>
      Loading (not disabled)
    </Button>
  </ButtonWrapper>
);

export const Sizes: Story<Props> = (args) => (
  <ButtonWrapper>
    <small>30 </small>
    <Button {...args} size="tiny" fitContent>
      tiny
    </Button>
    <Gap />
    <small>32 </small>
    <Button {...args} size="small" fitContent>
      Small
    </Button>
    <Gap />
    <small>40 </small>
    <Button {...args} size="normal" fitContent>
      Normal
    </Button>
    <Gap />
    <small>50 </small>
    <Button {...args} size="large" fitContent>
      Large
    </Button>
  </ButtonWrapper>
);

export const WithIcon: Story<Props> = (args) => (
  <ButtonWrapper>
    <Button {...args} size="tiny" icon="More" children={undefined} fitContent />
    <Gap />
    <Button {...args} icon="Plus">
      Left
    </Button>
    <Gap />
    <Button {...args} icon="ArrowForward" iconPosition="right">
      Right
    </Button>
    <Gap />
    <Button {...args} icon="Plus" disabled>
      Disabled
    </Button>
    <Gap />
    <Button {...args} icon="Plus" loading>
      Loading
    </Button>
    <Gap />
    <Button {...args} icon="Plus" loading disableWhenLoading={false}>
      Loading (not disabled)
    </Button>
  </ButtonWrapper>
);

export const Theme: Story<Props> = (args) => (
  <TwoButtonWrapper>
    <Row>
      <Button {...args}>Default</Button>
      <Button {...args} loading>
        Default
      </Button>
      <Button {...args} disabled>
        Default
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="primary">
        Primary
      </Button>
      <Button {...args} theme="primary" loading>
        Primary
      </Button>
      <Button {...args} theme="primary" disabled>
        Primary
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="black">
        Black
      </Button>
      <Button {...args} theme="black" loading>
        Black
      </Button>
      <Button {...args} theme="black" disabled>
        Black
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="danger">
        Danger
      </Button>
      <Button {...args} theme="danger" loading>
        Danger
      </Button>
      <Button {...args} theme="danger" disabled>
        Danger
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="sidebar">
        Sidebar
      </Button>
      <Button {...args} theme="sidebar" loading>
        Sidebar
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="toast">
        Toast
      </Button>
      <Button {...args} theme="toast" loading>
        Toast
      </Button>
      <Button {...args} theme="toast" disabled>
        Toast
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="map">
        Map
      </Button>
      <Button {...args} theme="map" loading>
        Map
      </Button>
      <Button {...args} theme="map" disabled>
        Map
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="white">
        White
      </Button>
      <Button {...args} theme="white" loading>
        White
      </Button>
      <Button {...args} theme="white" disabled>
        White
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="dark">
        Dark
      </Button>
      <Button {...args} theme="dark" loading>
        Dark
      </Button>
      <Button {...args} theme="dark" disabled>
        Dark
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="outline">
        Outline
      </Button>
      <Button {...args} theme="outline" loading>
        Outline
      </Button>
      <Button {...args} theme="outline" disabled>
        Outline
      </Button>
    </Row>
    <Gap />
    <Row>
      <Button {...args} theme="toolbar">
        Toolbar
      </Button>
      <Button {...args} theme="toolbar" loading>
        Toolbar
      </Button>
      <Button {...args} theme="toolbar" disabled>
        Toolbar
      </Button>
    </Row>
    <Gap />
  </TwoButtonWrapper>
);

export const Rounded: Story<Props> = (args) => (
  <ButtonWrapper>
    <Button
      {...args}
      size="small"
      icon="Close"
      fitContent
      children={undefined}
    />
    <Gap />
    <Button
      {...args}
      size="normal"
      icon="Location"
      theme="map"
      fitContent
      children={undefined}
    />
    <Gap />
    <Button {...args} />
  </ButtonWrapper>
);

Rounded.args = {
  rounded: true,
  children: 'Rounded',
};
