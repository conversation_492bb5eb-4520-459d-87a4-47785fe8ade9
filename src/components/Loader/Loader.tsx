import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';

import classNames from './Loader.module.css';

export type Props = {
  className?: string;
  dataTestId?: string;
  color?: string;
  iconName?: string;
  size?: number;
  loading?: boolean;
  inline?: boolean;
};

const Loader = ({
  className,
  dataTestId,
  color,
  iconName = 'Loader',
  size = 20,
  loading = true,
  inline,
}: Props) => {
  return (
    <div
      className={cx(
        classNames.loader,
        !inline && classNames.loader__fill,
        loading && classNames.loader__loading,
        className,
      )}
      data-testid={dataTestId}
    >
      <SvgIcon
        color={color}
        width={size}
        height={size}
        name={iconName}
        className={classNames.icon}
      />
    </div>
  );
};

export default Loader;
