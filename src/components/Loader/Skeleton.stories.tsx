import { Skeleton, SkeletonProps } from './Skeleton';
import { Meta, Story } from '@storybook/react';

export default {
  title: 'Components/Sekeleton',
  component: Skeleton,
  argTypes: {
    width: {
      description: 'width',
      defaultValue: '300px',
      type: 'string',
    },
  },
  args: {
    loading: true,
  },
} as Meta;

export const Loader: Story<SkeletonProps> = (args) => <Skeleton {...args} />;
