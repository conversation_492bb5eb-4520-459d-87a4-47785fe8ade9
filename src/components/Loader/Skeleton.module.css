.skeleton {
  position: relative;
  display: flex;
  background-color: var(--color-grey-30);
  border-radius: 4px;
  align-self: stretch;
  overflow: hidden;
}

.skeleton::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(90deg,
      #ffffff00 0%,
      #ffffff30 20%,
      #ffffff88 60%,
      #ffffff00 100%);
  animation: shimmer 2s infinite;
  content: '';
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}