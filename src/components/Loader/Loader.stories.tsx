import LoaderExample, { Props } from './Loader';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

export default {
  title: 'Components/Loader',
  component: LoaderExample,
  argTypes: {
    color: {
      description: 'Sets background color',
      type: 'string',
    },
    loading: {
      description: 'Sets loader visibility. Visible if loading = true',
      type: 'boolean',
    },
  },
  args: {
    loading: true,
  },
} as Meta;

const LoaderWrapper = styled.div`
  width: 200px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Loader: Story<Props> = (args) => (
  <LoaderWrapper>
    <LoaderExample {...args} />
  </LoaderWrapper>
);
