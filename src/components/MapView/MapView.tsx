import { Feature, LineString, Polygon, polygon } from '@turf/helpers';
import React, { useState } from 'react';
import { useStore, useStoreMap } from 'effector-react';
import omit from 'lodash/omit';

import {
  viewState$,
  interactiveLayerIdsByUuid$,
  mapMoved,
  mouseMoved,
  mousePressed,
  mouseReleased,
  allowDragging$,
  mouseOuted,
  setMapIsLoaded,
  setMapIsUnloaded,
} from 'models/map';
import { initMapMeasureDraw } from 'features/map-measure/MapMeasureRulerMode';
import {
  drawMeasuresSet,
  drawMeasuresUpdate,
  isMapDrawModeEnabled$,
  mapboxDrawInit,
} from 'features/map-measure/models/map-measure';
import { getFeatureMeasures } from 'features/map-measure/utils';

import LayerPlaceholder from '../LayerPlaceholder';
import { MapBase, MapBaseProps } from '../MapBase';

export function MapView({
  children,
  withoutRuler,
  withoutSidebar,
  ...props
}: MapBaseProps) {
  const viewState = useStore(viewState$);
  const allowDragging = useStore(allowDragging$);
  const isMapDrawModeEnabled = useStore(isMapDrawModeEnabled$);
  const interactiveLayerIds =
    useStoreMap(interactiveLayerIdsByUuid$, (items) => items[props.id]) || [];

  const [cursor, setCursor] = useState<string>('grab');

  return (
    <MapBase
      cursor={cursor}
      dragPan={allowDragging}
      interactiveLayerIds={interactiveLayerIds}
      onLoad={(e) => {
        setMapIsLoaded();
        if (withoutRuler) {
          return;
        }
        const draw = initMapMeasureDraw(e.target, {
          onAddPoint: () => drawMeasuresUpdate(),
          onDrawPolygon: (e) => {
            const mapPolygon = polygon([e.coordinates]);
            const measures = getFeatureMeasures(mapPolygon);
            drawMeasuresSet({ measures, isDone: true });
          },
          onDrawUpdate: (e) => {
            const mapPolygon = e.features[0]!;
            const measures = getFeatureMeasures(
              mapPolygon as Feature<Polygon | LineString>,
            );
            drawMeasuresSet({ measures, isDone: false });
          },
          onCreateLine: (e) => {
            const measures = getFeatureMeasures(e.features[0]);
            drawMeasuresSet({ measures, isDone: true });
          },
        });
        mapboxDrawInit(draw);
      }}
      onRemove={() => setMapIsUnloaded()}
      onMouseMove={(event) => {
        if (isMapDrawModeEnabled) {
          setCursor('unset');
        } else {
          setCursor(event.features?.length !== 0 ? 'pointer' : 'grab');
        }
        mouseMoved(event);
      }}
      onMouseOut={mouseOuted}
      onMouseDown={mousePressed}
      onMouseUp={mouseReleased}
      preserveDrawingBuffer={true}
      onMove={mapMoved}
      // will be not needed if everywhere will be separate MapFieldInstance
      {...(withoutSidebar ? omit(viewState, 'padding') : viewState)}
      {...props}
    >
      <>
        {/* Placeholder for the map layers and order of layers. Must be on interfaces with ruler */}
        <LayerPlaceholder id="layer-placeholder" />
        {children}
      </>
    </MapBase>
  );
}
