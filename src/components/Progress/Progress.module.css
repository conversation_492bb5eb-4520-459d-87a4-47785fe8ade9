.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.vertical.container {
  height: 100%;
}

.track {
  width: 100%;
  height: 8px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(55, 210, 121, 0.30);
  display: flex;
}

.vertical .track {
  height: 100%;
  width: 8px;
  align-items: flex-end;
}

.bar {
  background-color: var(--color-primary);
}

.text {
  font-size: 12px;
  line-height: 16px;
  color: #5e5e5e;
}
