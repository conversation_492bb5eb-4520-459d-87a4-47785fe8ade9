import cx from 'classnames';

import classNames from './Progress.module.css';

export type Props = {
  value: number;
  text?: string;
  isVertical?: boolean;
};

const Progress = ({ value, isVertical, text }: Props) => {
  const fillProp = isVertical ? 'height' : 'width';
  const restProp = isVertical ? 'width' : 'height';
  return (
    <div
      className={cx(classNames.container, isVertical && classNames.vertical)}
    >
      <div className={classNames.track}>
        <div
          className={classNames.bar}
          style={{ [fillProp]: `${value * 100}%`, [restProp]: '100%' }}
        />
      </div>
      {!!text && <span className={classNames.text}>{text}</span>}
    </div>
  );
};

export default Progress;
