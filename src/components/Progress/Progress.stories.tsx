import ProgressExample, { Props } from './Progress';
import { styled } from '@linaria/react';
import { Meta, <PERSON> } from '@storybook/react';

export default {
  title: 'Components/Progress',
  component: ProgressExample,
  argTypes: {
    value: {
      description: 'Sets Progress from 0 to 1',
      control: {
        type: 'range',
        min: 0,
        max: 1,
        step: 0.05,
      },
    },
  },
  args: {
    value: 0.4,
    text: '4 hs 31 m left',
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <ProgressExample {...args} />
  </Wrapper>
);
