.switcher {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 2px;
  background-color: var(--color-black);
  color: var(--color-white);
  border-radius: 20px;
}

.options {
  height: 100%;
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.option {
  height: 100%;
  max-width: 120px;
  white-space: nowrap;
  line-height: 1;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  padding: 0 12px;
  border-radius: 20px;
}

.hiddenOption {
  cursor: pointer;
}

.option__active {
  background-color: var(--color-grey-80);
}

.iconButton {
  transform: rotate(90deg);
  padding: 0 12px;
}

.link {
  min-width: 180px;
}

.dropdown {
  max-height: 70vh;
  overflow-y: auto;
}

.label {
  overflow: hidden;
  text-overflow: ellipsis;
}