import { useMemo } from 'react';
import cx from 'classnames';

import { Text } from 'components/Text';
import IconButton from 'components/IconButton/IconButton';
import Popover, { DefaultBox, NavBox } from 'components/Popover/Popover';
import Button from 'components/Button/Button';
import MenuLink from 'components/MenuLink';
import SimpleLink from 'components/SimpleLink';
import { Tooltip } from 'components/Tooltip/Tooltip';
import { Option } from 'components/Dropdown/types';

import classNames from './MenuSwitcher.module.css';

type MenuSwitcherProps = {
  value?: string;
  options: Option[];
  isDropdownView?: boolean;
  onClick: (val: string) => void;
  visibleCount?: number;
};

const DropdownView = ({
  value,
  options,
  onClick,
}: {
  value?: string;
  options: Option[];
  onClick: (val: string) => void;
}) => (
  <div>
    <Popover
      trigger="click"
      placement="bottom-start"
      interactive
      render={(attrs, _, instance) => (
        <DefaultBox {...attrs}>
          <div className={classNames.dropdown}>
            {options.map((option) => (
              <MenuLink
                as={SimpleLink}
                active={option.id === value}
                className={classNames.link}
                onClick={() => {
                  onClick(option.id);
                  instance?.hide();
                }}
              >
                <Text
                  weight={option.id === value ? 'medium' : 'normal'}
                  size={14}
                >
                  {option.label}
                </Text>
              </MenuLink>
            ))}
          </div>
        </DefaultBox>
      )}
    >
      <Button
        size="normal"
        theme="map"
        icon={'Expand'}
        iconPosition="right"
        rounded
      >
        {options.find((option) => option.id === value)?.label}
      </Button>
    </Popover>
  </div>
);

const ListView = ({
  value,
  visibleOptions,
  dropdownOptions,
  onClick,
}: {
  value?: string;
  visibleOptions: Option[];
  dropdownOptions: Option[];
  onClick: (val: string) => void;
}) => (
  <div className={classNames.switcher}>
    <ul className={classNames.options}>
      {visibleOptions.map((option) => (
        <Tooltip
          placement="bottom"
          arrow
          disabled={false}
          key={option.id}
          content={option.label}
        >
          <li
            onClick={() => onClick(option.id)}
            className={cx(
              classNames.option,
              option.id === value && classNames.option__active,
            )}
          >
            <Text size={12} weight="medium" className={classNames.label}>
              {option.label}
            </Text>
          </li>
        </Tooltip>
      ))}
    </ul>

    {dropdownOptions.length > 0 && (
      <Popover
        trigger="click"
        interactive
        offset={[0, 4]}
        render={(attrs, _, instance) => (
          <NavBox {...attrs}>
            <div className={classNames.dropdown}>
              {dropdownOptions.map((option) => (
                <MenuLink
                  as={SimpleLink}
                  onClick={() => {
                    onClick(option.id);
                    instance?.hide();
                  }}
                  key={option.id}
                  className={classNames.hiddenOption}
                >
                  {
                    <Text size={12} weight="medium">
                      {option.label}
                    </Text>
                  }
                </MenuLink>
              ))}
            </div>
          </NavBox>
        )}
        placement="top"
      >
        <IconButton
          className={classNames.iconButton}
          iconSize={16}
          icon="More"
        />
      </Popover>
    )}
  </div>
);

const MenuSwitcher = ({
  value,
  options = [],
  isDropdownView = false,
  visibleCount = 3,
  onClick,
}: MenuSwitcherProps) => {
  const { visibleOptions, dropdownOptions } = useMemo(() => {
    const selectedOption = options.find((option) => option.id === value);
    const isInitialVisible =
      !selectedOption ||
      options.slice(0, visibleCount).includes(selectedOption);
    let visibleOptions = options;
    if (!isInitialVisible) {
      visibleOptions = [selectedOption];
      visibleOptions = visibleOptions.concat(
        ...options.filter((opt) => opt.id !== selectedOption?.id),
      );
    }
    visibleOptions = visibleOptions.slice(0, visibleCount);

    const dropdownOptions = options.filter(
      (option) => !visibleOptions.map((o) => o.id).includes(option.id),
    );
    return { visibleOptions, dropdownOptions };
  }, [options, value, visibleCount]);

  if (options.length === 0) {
    return null;
  }

  return isDropdownView ? (
    <DropdownView value={value} options={options} onClick={onClick} />
  ) : (
    <ListView
      value={value}
      visibleOptions={visibleOptions}
      dropdownOptions={dropdownOptions}
      onClick={onClick}
    />
  );
};

export default MenuSwitcher;
