import { ReactNode, useEffect, useMemo } from 'react';

import {
  slotItemMounted,
  slotItemUpdated,
  slotItemUnmounted,
} from 'models/slots';

type Props = {
  id: string;
  children: ReactNode;
};

// We know what we are doing here
/* eslint-disable react-hooks/exhaustive-deps */

function RenderInSlot({ id, children }: Props) {
  const ourId = useMemo(() => `slot-item-${Math.random()}`, []);
  const slotItem = { itemId: ourId, slotId: id, element: children };

  useEffect(() => {
    slotItemMounted(slotItem);
    return () => {
      slotItemUnmounted(slotItem);
    };
  }, [id]);

  useEffect(() => {
    slotItemUpdated(slotItem);
  }, [children]);

  // Not rendered here
  return null;
}

export default RenderInSlot;
