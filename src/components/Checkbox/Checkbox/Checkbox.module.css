.container {
  -webkit-tap-highlight-color: transparent;
  display: inline-flex;
  gap: 8px;
  align-items: center;
  position: relative;
  outline: none;
  min-height: 20px;
  cursor: pointer;
}

.container > input {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
}

.container__disabled {
  cursor: not-allowed;
}

.label {
  font-size: 14px;
  line-height: 18px;
}

.checkmark {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border-width: 1px;
  border-style: solid;
  border-color: var(--color-grey-70);
  background-color: transparent;
  transition: color 0.3s, border-color 0.3s, background-color 0.3s;
  color: var(--color-white);
}

.checkmark_rounded {
  border-radius: 50%;
}

.checkmark__checked {
  border-color: var(--color-black);
  background-color: var(--color-black);
}

.container__disabled .checkmark {
  border-color: var(--color-grey-30);
  background-color: var(--color-grey-30);
  color: var(--color-grey-70);
}

/* Theme CheckboxGroup for the new filter */
.revert {
  flex-direction: row-reverse;
  justify-content: space-between;
  padding-left: 0;
}

.revert .checkmark {
  position: relative;
}
