import { styled } from '@linaria/react';
import { <PERSON>a, <PERSON> } from '@storybook/react';

import CheckboxExample, { Props } from './Checkbox';

export default {
  title: 'Components/Checkbox',
  component: CheckboxExample,
  args: {
    defaultChecked: true,
    label: 'Rapeseed, winter',
    disabled: false,
    indeterminate: false,
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <CheckboxExample {...args} />
  </Wrapper>
);
