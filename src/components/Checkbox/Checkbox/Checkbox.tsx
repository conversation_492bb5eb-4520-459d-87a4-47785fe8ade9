import React, {
  useCallback,
  forwardRef,
  useState,
  useContext,
  useEffect,
  FC,
  ChangeEvent,
  InputHTMLAttributes,
  ReactNode,
} from 'react';
import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import { CheckboxGroupContext } from '../context';

import classNames from './Checkbox.module.css';

export interface Props
  extends Omit<
    InputHTMLAttributes<HTMLInputElement>,
    'onChange' | 'type' | 'size'
  > {
  label?: ReactNode;
  indeterminate?: boolean;
  checked?: boolean;
  defaultChecked?: boolean;
  disabled?: boolean;
  revert?: boolean;
  rounded?: boolean;
  onChange?: (value: boolean, indeterminate: boolean) => void;
}

const Checkbox: FC<Props> = forwardRef<HTMLInputElement, Props>(
  function Checkbox(props, ref) {
    const {
      label,
      disabled = false,
      indeterminate = false,
      checked,
      defaultChecked = false,
      className,
      style,
      id,
      revert,
      rounded,
      onChange,
      ...restProps
    } = props;

    const { onGroupChange, groupValue, groupDisabled, disabledAll } =
      useContext(CheckboxGroupContext);

    const [isChecked, setIsChecked] = useState<boolean>(defaultChecked);
    const [isIndeterminate, setIsIndeterminate] =
      useState<boolean>(indeterminate);
    const [isDisabled, setIsDisabled] = useState<boolean>(disabled);

    const handleInputChange = useCallback(
      (event: ChangeEvent<HTMLInputElement>) => {
        const checkboxChecked = event.target.checked;

        if (onChange) {
          onChange(checkboxChecked, isIndeterminate);
        }

        if (onGroupChange) {
          onGroupChange(event.target.id);
        }

        if (checked === undefined) {
          setIsChecked(checkboxChecked);
        }
      },
      [onGroupChange, onChange, setIsChecked, checked, isIndeterminate],
    );

    const renderIcon = () => {
      if (isIndeterminate) {
        return <SvgIcon name={'Minimize'} width={16} height={16} />;
      }

      if (isChecked) {
        return <SvgIcon name="CheckMark" width={16} height={16} />;
      }
      return null;
    };

    useEffect(() => {
      if (indeterminate !== isIndeterminate) {
        setIsIndeterminate(indeterminate);
      }

      if (groupValue && id) {
        const newValue = groupValue.includes(id);
        setIsChecked(newValue);
      } else if (checked !== undefined && checked !== isChecked) {
        setIsChecked(checked);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [indeterminate, groupValue, groupDisabled, checked]);

    useEffect(() => {
      if (groupDisabled && id) {
        const newDisabled = disabledAll || groupDisabled.includes(id);
        setIsDisabled(newDisabled);
      } else if (disabled !== isDisabled) {
        setIsDisabled(disabled);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [groupDisabled, disabled]);

    const checkboxIcon = renderIcon();

    return (
      <label
        className={cx(
          classNames.container,
          isDisabled && classNames.container__disabled,
          revert && classNames.revert,
          className,
        )}
        style={style}
      >
        <input
          ref={ref}
          id={id}
          type="checkbox"
          disabled={isDisabled}
          checked={isChecked}
          onChange={handleInputChange}
          {...restProps}
        />
        <span
          className={cx(
            rounded && classNames.checkmark_rounded,
            classNames.checkmark,
            (isChecked || isIndeterminate) && classNames.checkmark__checked,
          )}
        >
          {checkboxIcon}
        </span>
        {label ? <span className={classNames.label}>{label}</span> : null}
      </label>
    );
  },
);

Checkbox.displayName = 'Checkbox';

export default Checkbox;
