import React, {
  forwardRef,
  useCallback,
  useState,
  useMemo,
  useImperativeHandle,
  ForwardRefRenderFunction,
} from 'react';
import cx from 'classnames';

import CheckboxGroupContextProvider from '../context';
import Checkbox from '../Checkbox/Checkbox';

import { ICheckboxGroupProps } from './types';
import classNames from './CheckboxGroup.module.css';

type CheckboxGroupHandle = {
  selectAll: () => void;
  unselectAll: () => void;
};

const CheckboxGroup: ForwardRefRenderFunction<
  CheckboxGroupHandle,
  ICheckboxGroupProps
> = (props, ref) => {
  const {
    title = '',
    showParentBox = false,
    options,
    defaultChecked = [],
    disabledAll = false,
    disabledCheckboxes = [],
    value,
    name,
    className,
    style,
    revert,
    checkboxProps,
    onChange = () => {},
  } = props;

  const [intervalValue, setGroupValue] = useState<string[]>(defaultChecked);
  const groupValue: string[] = value ? value : intervalValue;

  const showLegend = useMemo(
    () => Boolean(!showParentBox && title),
    [showParentBox, title],
  );
  const showLegendWithCheckbox = useMemo(
    () => Boolean(showParentBox && title),
    [showParentBox, title],
  );
  const { checked, indeterminate } = useMemo(
    () => ({
      checked: groupValue.length === options.length,
      indeterminate:
        groupValue.length > 0 && groupValue.length < options.length,
    }),
    [groupValue, options],
  );

  const isEmptyEnabledBoxes = useMemo(
    () =>
      options.some(
        (opt) =>
          !disabledCheckboxes.includes(opt.key) &&
          !groupValue.includes(opt.key),
      ),
    [options, groupValue, disabledCheckboxes],
  );
  const noChecked = groupValue.length === 0;

  const handleChange = useCallback(
    (id: string) => {
      if (!id) {
        return;
      }

      const newValue = [...groupValue];
      const indexValue = groupValue.findIndex((value) => id === value);

      if (indexValue === -1) {
        newValue.push(id);
      } else {
        newValue.splice(indexValue, 1);
      }

      setGroupValue(newValue);
      onChange(newValue);
    },
    [groupValue, onChange],
  );

  const setAllChecked = () => {
    const newValue = options.reduce((newArray: string[], opt) => {
      const isOptionDisabled = disabledCheckboxes.includes(opt.key);
      const isOptionChecked = groupValue.includes(opt.key);

      if (!(isOptionDisabled && !isOptionChecked)) {
        newArray.push(opt.key);
      }
      return newArray;
    }, []);
    setGroupValue(newValue);
    onChange(newValue);
  };

  const setAllUnchecked = () => {
    const newValue = options.reduce((newArray: string[], opt) => {
      if (
        disabledCheckboxes.includes(opt.key) &&
        groupValue.includes(opt.key)
      ) {
        newArray.push(opt.key);
      }
      return newArray;
    }, []);
    setGroupValue(newValue);
    onChange(newValue);
  };

  const parentBoxHandler = () => {
    // если нет выбранных или выбраны частично (есть пустые enabled)
    if (noChecked || (indeterminate && isEmptyEnabledBoxes)) {
      setAllChecked();
    } else {
      setAllUnchecked();
    }
  };

  const groupOptions = useMemo(() => {
    return options.map((option) => {
      return (
        <Checkbox
          id={option.key}
          key={option.key}
          name={name}
          label={option.value}
          revert={revert}
          {...checkboxProps}
        />
      );
    });
  }, [options, checkboxProps, name, revert]);

  useImperativeHandle(ref, () => ({
    selectAll: setAllChecked,
    unselectAll: setAllUnchecked,
  }));

  return (
    <CheckboxGroupContextProvider
      value={{
        onGroupChange: handleChange,
        groupValue,
        groupDisabled: disabledCheckboxes,
        disabledAll,
      }}
    >
      <fieldset className={cx(classNames.container, className)} style={style}>
        {showLegendWithCheckbox && (
          <div className={classNames.title}>
            <Checkbox
              name="parentBox"
              label={title}
              onChange={parentBoxHandler}
              checked={checked}
              indeterminate={indeterminate}
              disabled={disabledAll}
              {...checkboxProps}
            />
          </div>
        )}
        {showLegend && (
          <div className={classNames.title}>
            <span>{title}</span>
          </div>
        )}
        <div
          className={classNames.options}
          style={{ paddingLeft: showLegendWithCheckbox ? 30 : 0 }}
        >
          {groupOptions}
        </div>
      </fieldset>
    </CheckboxGroupContextProvider>
  );
};

CheckboxGroup.displayName = 'CheckboxGroup';

export default forwardRef(CheckboxGroup);
