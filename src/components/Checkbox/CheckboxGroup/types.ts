import { CSSProperties, ReactNode } from 'react';
import { Props as CheckboxProps } from '../Checkbox/Checkbox';

export interface ICheckboxGroupProps {
  title?: ReactNode;
  showParentBox?: boolean;
  options: OptionType[];
  defaultChecked?: string[];
  disabledCheckboxes?: string[];
  disabledAll?: boolean;
  checkboxProps?: Partial<Pick<CheckboxProps, 'className' | 'style'>>;
  className?: string;
  style?: CSSProperties;
  name?: string;
  value?: string[];
  revert?: boolean;
  onChange?: (selected: string[]) => void;
}

export type OptionType = {
  key: string;
  value: ReactNode;
};
