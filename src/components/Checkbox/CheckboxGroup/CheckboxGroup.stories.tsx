import CheckboxGroupExample from './CheckboxGroup';
import { styled } from '@linaria/react';
import { ICheckboxGroupProps } from './types';
import { Meta, Story } from '@storybook/react';
import { useState } from 'react';

const options = [
  {
    key: 'alfalfa',
    value: 'Alfalfa',
  },
  {
    key: 'beet',
    value: 'Beet, sugar',
  },
  {
    key: 'corn',
    value: 'Corn, grain',
  },
  {
    key: 'rapeseed',
    value: 'Rapeseed, winter',
  },
  {
    key: 'sunflower',
    value: 'Sunflower',
  },
  {
    key: 'wheat_hard',
    value: 'Wheat hard, spring',
  },
];

export default {
  title: 'Components/CheckboxGroup',
  component: CheckboxGroupExample,
  args: {
    title: '',
    showParentBox: false,
    options,
    defaultChecked: [],
    disabledAll: false,
    disabledCheckboxes: [],
    name: '',
    className: '',
    style: {},
    checkboxProps: [],
    onChange: () => {},
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 60px;
`;

export const Default: Story<ICheckboxGroupProps> = (args) => {
  const [selected, setSelected] = useState<string[]>([]);
  return (
    <Wrapper>
      <CheckboxGroupExample
        {...args}
        onChange={(opts: string[]) => {
          setSelected(opts);
        }}
      />
      <p>{selected.join(', ')}</p>
    </Wrapper>
  );
};

export const DefaultChecked: Story<ICheckboxGroupProps> = (args) => {
  const [selected, setSelected] = useState<string[]>(['corn', 'rapeseed']);
  return (
    <Wrapper>
      <CheckboxGroupExample
        {...args}
        onChange={(opts: string[]) => {
          setSelected(opts);
        }}
        defaultChecked={['corn', 'rapeseed']}
      />
      <p>{selected.join(', ')}</p>
    </Wrapper>
  );
};

export const Grouped: Story<ICheckboxGroupProps> = (args) => {
  const [selected, setSelected] = useState<string[]>([]);
  return (
    <Wrapper>
      <CheckboxGroupExample
        {...args}
        showParentBox
        title={'Group of crops'}
        name={'group'}
        onChange={(opts: string[]) => {
          setSelected(opts);
        }}
      />
      <p>{selected.join(', ')}</p>
    </Wrapper>
  );
};
