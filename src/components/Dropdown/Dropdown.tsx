import { ChangeEvent, Fragment, useState } from 'react';
import Downshift from 'downshift';
import isString from 'lodash/isString';
import cx from 'classnames';

import Popover from 'components/Popover/Popover';
import SvgIcon from 'components/SvgIcon';
import TextInput from 'components/TextInput/TextInput';
import Document<PERSON>eyHandler from 'components/DocumentKeyHandler/DocumentKeyHandler';

import useOutsideClick from 'utils/use-outside-click';

import { DropdownProps, GetInputPropsOptionsRef, Option } from './types';
import classNames from './Dropdown.module.css';

const Dropdown = ({
  className,
  popoverClassName,
  value,
  options,
  optionGroups = [
    {
      id: 'default-group',
      options: options || [],
    },
  ],
  markedOptionValue,
  searchPlaceholder,
  onChange,
  popoverAutoWidth,
  withSearchbox = false,
  matchOption = (option, query) => {
    return (
      isString(option?.label) &&
      option?.label.toLowerCase().includes(query.toLowerCase().trim())
    );
  },
  renderTrigger,
  bottomRender,
  disabled,
  onSearch,
}: DropdownProps) => {
  const { ref, isVisible, setIsVisible } =
    useOutsideClick<HTMLDivElement>(false);
  const [searchValue, setSearchValue] = useState('');

  const findOptionById = (id: string) => {
    for (const group of optionGroups) {
      const item = group.options.find((item) => item.id === id);
      if (item) {
        return item;
      }
    }
    return null;
  };

  const resetSearch = () => {
    setSearchValue('');
    onSearch?.('');
  };

  const handleChange = (selection: Option | null) => {
    onChange(selection ? selection.id : null);
    setIsVisible(false);
  };

  const handleChangeSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
    onSearch?.(e.target.value);
  };

  const handleToggle = () => {
    setIsVisible(!isVisible);
    resetSearch();
  };

  const itemToString = (i: Option | null) =>
    i && isString(i.label) ? i.label : '';
  const selectedOption = value ? findOptionById(value) : null;

  return (
    <div className={className} ref={ref}>
      <DocumentKeyHandler hotkey="Escape" onPress={() => setIsVisible(false)} />
      <Downshift
        itemToString={itemToString}
        selectedItem={selectedOption}
        onChange={handleChange}
      >
        {({ getInputProps, getMenuProps, getItemProps, selectedItem }) => (
          <div>
            <Popover
              disabled={disabled}
              visible={isVisible}
              content={
                <div
                  className={cx(
                    classNames.popover,
                    popoverAutoWidth && classNames.popover__fit,
                    popoverClassName && popoverClassName,
                  )}
                >
                  {withSearchbox && (
                    <div className={classNames['search-box']}>
                      <TextInput
                        clearable
                        onClear={resetSearch}
                        disabled={disabled}
                        withSearchIcon
                        {...(getInputProps({
                          onChange: handleChangeSearch,
                          value: searchValue,
                          placeholder: searchPlaceholder || '',
                        }) as GetInputPropsOptionsRef)}
                      />
                    </div>
                  )}
                  <div
                    className={classNames['options-wrapper']}
                    {...getMenuProps({}, { suppressRefError: true })}
                  >
                    {optionGroups.map((group) => {
                      const filteredOptions = searchValue
                        ? group.options.filter((option) =>
                            matchOption(option, searchValue),
                          )
                        : group.options;

                      if (filteredOptions.length === 0) {
                        return null;
                      }

                      return (
                        <Fragment key={group.id}>
                          {group.label && (
                            <div className={classNames.groupLabel}>
                              {group.label}
                            </div>
                          )}
                          <ul className={classNames.options}>
                            {filteredOptions.map((option) => (
                              <li
                                key={option.id}
                                className={cx(
                                  classNames.option,
                                  markedOptionValue === +option.id &&
                                    classNames.markedOption,
                                )}
                                {...getItemProps({
                                  item: option,
                                  disabled: option.disabled,
                                })}
                              >
                                <div
                                  className={cx(
                                    classNames.optionContainer,
                                    option.disabled &&
                                      classNames.optionDisabled,
                                    selectedItem === option &&
                                      'dropdownItemActive',
                                  )}
                                >
                                  {option.label}
                                  <div className={classNames.optionRight}>
                                    {option.labelRight}
                                    {selectedItem === option && (
                                      <SvgIcon
                                        name="CheckMark"
                                        width={16}
                                        height={16}
                                      />
                                    )}
                                  </div>
                                </div>
                                {option.description && (
                                  <div className={classNames.description}>
                                    {option.description}
                                  </div>
                                )}
                              </li>
                            ))}
                            {bottomRender && (
                              <li className={classNames.option}>
                                {bottomRender}
                              </li>
                            )}
                          </ul>
                        </Fragment>
                      );
                    })}
                  </div>
                </div>
              }
              placement={'bottom-start'}
              offset={[0, 4]}
              interactive
            >
              {renderTrigger({
                isOpen: isVisible,
                toggle: handleToggle,
                selectedItem,
              })}
            </Popover>
          </div>
        )}
      </Downshift>
    </div>
  );
};

export default Dropdown;
