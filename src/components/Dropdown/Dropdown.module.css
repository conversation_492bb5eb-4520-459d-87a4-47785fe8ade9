.popover {
  min-width: 288px;
}

.popover__fit {
  min-width: auto;
}

.search-box {
  position: relative;
  z-index: 3;
  padding: 0 10px 10px;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}

.search-box:after {
  position: absolute;
  pointer-events: none;
  content: '';
  height: 10px;
  bottom: -11px;
  left: 0;
  right: 0;
  background-image: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.04) 0%,
    rgba(0, 0, 0, 0) 100%
  );
}

.options-wrapper {
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  max-height: 280px;
}

.search-box + .options-wrapper {
  padding-top: 10px;
}

.options {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.groupLabel {
  color: var(--color-grey-100);
  padding: 0px 15px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 1px;
}

.options + .groupLabel {
  margin-top: 20px;
}

.option {
  display: flex;
  flex-direction: column;
  color: #222;
  font-size: 14px;
  line-height: 1.25rem;
  font-weight: normal;
  padding: 0 8px;
  justify-content: center;
  cursor: pointer;
}

.option :global(.dropdownItemActive) {
  font-weight: 500;
  color: var(--color-black);
}

.optionContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  padding: 6px 8px;
}

.optionRight {
  display: flex;
  align-items: center;
  gap: 4px;
}

.option:hover .optionContainer:not(.optionDisabled),
.option :global(.dropdownItemActive) {
  background-color: var(--color-grey-10);
}

.optionDisabled {
  color: var(--color-grey-100);
  cursor: not-allowed;
}

.description {
  color: var(--color-grey-100);
  white-space: pre-line;
  font-size: 12px;
  line-height: 16px;
}

.markedOption {
  position: relative;
  padding: 16px 8px;
}

.markedOption:before,
.markedOption:after {
  content: '';
  position: absolute;
  width: 100%;
  left: 0;
  height: 1px;
  background-color: var(--color-grey-10);
}

.markedOption:before {
  top: 8px;
}

.markedOption:after {
  bottom: 8px;
}
