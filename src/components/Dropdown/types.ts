import React, { ChangeEventHandler, ReactElement, Ref } from 'react';

export type TriggerOptions = {
  isOpen: boolean;
  toggle: () => void;
  selectedItem: Option | null;
};

export interface DropdownProps {
  className?: string;
  popoverClassName?: string;
  options?: Option[];
  optionGroups?: OptionGroup[];
  searchPlaceholder?: string;
  withSearchbox?: boolean;
  onSearch?: (value: string) => void;
  matchOption?: (option: Option, query: string) => boolean;
  onChange: (id: string | null) => void;
  value: string | null;
  markedOptionValue?: number | null;
  popoverAutoWidth?: boolean;
  disabled?: boolean;
  renderTrigger: (options: TriggerOptions) => ReactElement<any>;
  bottomRender?: ReactElement<any> | null;
}

export type Option = {
  id: string;
  label: string | React.ReactNode;
  labelRight?: string | React.ReactNode;
  description?: string;
  disabled?: boolean;
};

export type OptionGroup = {
  id: string;
  label?: React.ReactNode;
  options: Option[];
};

export interface GetInputPropsOptions
  extends React.HTMLProps<HTMLInputElement> {
  disabled?: boolean;
  onChange: ChangeEventHandler<HTMLInputElement>;
}

export interface GetInputPropsOptionsRef extends GetInputPropsOptions {
  ref?: Ref<HTMLInputElement>;
}
