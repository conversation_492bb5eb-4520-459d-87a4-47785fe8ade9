import React, { useRef, useEffect } from 'react';
import ReactSlider from 'react-slider';

import { DirectionSliderProps } from './types';

import classNames from './DirectionSlider.module.css';

const DirectionSlider = ({ onChange, value }: DirectionSliderProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<ReactSlider<number>>(null);

  useEffect(() => {
    if (typeof ResizeObserver === 'undefined') {
      return;
    }
    const cr = containerRef.current;
    const resizeObserver = new ResizeObserver(() => {
      sliderRef.current?.resize();
    });
    resizeObserver.observe(cr!);

    return () => {
      resizeObserver.unobserve(cr!);
    };
  }, []);

  return (
    <div ref={containerRef} className={classNames.container}>
      <ReactSlider
        ref={sliderRef}
        className={classNames.slider}
        trackClassName="track-slider"
        min={0}
        max={360}
        value={value}
        renderThumb={(props, state) => (
          <div
            {...props}
            className={classNames.thumb}
            style={{
              ...props.style,
              transform: state.index ? 'translateX(1px)' : 'translateX(0px)',
            }}
          />
        )}
        onChange={onChange}
      />
    </div>
  );
};

export default DirectionSlider;
