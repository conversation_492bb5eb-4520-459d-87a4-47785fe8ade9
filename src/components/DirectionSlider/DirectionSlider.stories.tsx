import DirectionSliderExample from './DirectionSlider';
import { styled } from '@linaria/react';
import { DirectionSliderProps } from './types';
import { Meta, Story } from '@storybook/react';
import { useState } from 'react';

export default {
  title: 'Components/3 Sliders/Direction Slider',
  component: DirectionSliderExample,
  argTypes: {},
  args: {
    value: 0,
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const DirectionSlider: Story<DirectionSliderProps> = (args) => {
  const [value, setValue] = useState(args.value);
  return (
    <Wrapper>
      <DirectionSliderExample
        {...args}
        value={value}
        onChange={(value: number) => {
          setValue(value);
        }}
      />
    </Wrapper>
  );
};
