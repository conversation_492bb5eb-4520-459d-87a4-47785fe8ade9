.container {
  padding-top: 12px;
  padding-bottom: 12px;
}

.thumb {
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-black);
  background-color: white;
  border-radius: 50%;
  cursor: grab;
  position: absolute;
  top: -7px;
}

.thumb:focus-visible {
  outline: none;
}

/* Hack to connect styles with react-slider */
.slider :global(.track-slider) {
  height: 2px;
  background: var(--color-black);
}

.slider :global(.track-slider-1) {
  background: var(--color-grey-30);
}
