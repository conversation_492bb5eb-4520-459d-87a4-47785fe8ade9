import { ReactNode } from 'react';
import cx from 'classnames';

import classNames from './Text.module.css';

export type TextProps = {
  children: ReactNode;
  size: 24 | 18 | 16 | 14 | 12;
  weight?: 'normal' | 'medium';
  align?: 'left' | 'center' | 'right';
  isParagraph?: boolean;
  title?: string;
  className?: string;
};

export const Text = ({
  children,
  size,
  weight = 'normal',
  align,
  title,
  isParagraph,
  className,
}: TextProps) => {
  return (
    <p
      className={cx(
        classNames.text,
        !!size && classNames[`size__${size}`],
        !!weight && classNames[`weight__${weight}`],
        !!align && classNames[`align__${align}`],
        !!isParagraph && classNames.paragraph,
        className,
      )}
      title={title}
    >
      {children}
    </p>
  );
};
