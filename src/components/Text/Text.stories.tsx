import { Meta, <PERSON> } from '@storybook/react';

import { Text, TextProps } from './Text';

export default {
  title: 'Components/Text',
  component: Text,
} as Meta;

export const Default: Story<TextProps> = () => (
  <div style={{ margin: 16 }}>
    <Text size={24} weight="medium">
      Hello world 24/130
    </Text>
    <Text size={18} weight="medium">
      Hello world 18/130
    </Text>
    <Text size={16} weight="medium">
      Hello world 16/130
    </Text>
    <Text size={14}>Hello world 14/130</Text>
    <Text size={12}>Hello world 12/130</Text>

    <Text size={16} weight="medium" align="center">
      Align: center
    </Text>
  </div>
);
