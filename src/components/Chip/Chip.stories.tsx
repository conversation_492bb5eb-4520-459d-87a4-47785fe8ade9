import ChipExample, { Props } from './Chip';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

export default {
  title: 'Components/Chip',
  component: ChipExample,
  args: {
    color: '#1CC98A',
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <ChipExample {...args}>+88.8%</ChipExample>
  </Wrapper>
);
