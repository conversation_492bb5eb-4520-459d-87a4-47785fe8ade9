import React, { ReactNode } from 'react';

import classNames from './Chip.module.css';

export type Props = {
  color: string;
  textColor?: string;
  children?: ReactNode;
};

const Chip = ({ children, color, textColor = 'var(--color-black)' }: Props) => {
  return (
    <div
      className={classNames.chip}
      style={{
        color: textColor,
        backgroundColor: color,
      }}
    >
      {children}
    </div>
  );
};

export default Chip;
