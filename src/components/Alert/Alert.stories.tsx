import { Story } from '@storybook/react';

import { Alert } from './Alert';

export default {
  component: Alert,
  title: 'Components/Alert',
};

export const Default: Story = () => (
  <div
    style={{
      margin: 24,
      width: 824,
      display: 'grid',
      gridTemplateColumns: '1fr 1fr',
      gridGap: 24,
    }}
  >
    <Alert title="Warning" details="Details" mode="dark" />
    <Alert title="Warning" details="Details" />
    <Alert title="Failure" details="Details" type="failure" mode="dark" />
    <Alert title="Failure" details="Details" type="failure" />
    <Alert title="Success" details="Details" type="success" mode="dark" />
    <Alert title="Success" details="Details" type="success" />
    <Alert title="Title" details="Details" size="small" mode="dark" />
  </div>
);
