import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import { Text } from 'components/Text';

import classNames from './Alert.module.css';

type AlertProps = {
  title: string;
  details: string;
  type?: 'warning' | 'failure' | 'success';
  mode?: 'default' | 'dark';
  size?: 'normal' | 'small';
};

export const Alert = ({
  title,
  details,
  type = 'warning',
  mode = 'default',
  size = 'normal',
}: AlertProps) => {
  let icon = 'WarningFilled';
  if (type === 'failure') {
    icon = 'FailureFilled';
  } else if (type === 'success') {
    icon = 'SuccessFilled';
  }
  return (
    <div
      className={cx(
        classNames.alertContainer,
        mode === 'dark' && classNames.darkContainer,
        classNames[`type__${type}`],
        classNames[`size__${size}`],
      )}
    >
      <SvgIcon
        name={icon}
        width={24}
        height={24}
        className={classNames.alertIcon}
      />
      <Text
        size={size === 'small' ? 14 : 16}
        weight="medium"
        className={classNames.alertTitle}
      >
        {title}
      </Text>
      <Text
        size={size === 'small' ? 12 : 14}
        isParagraph
        className={classNames.alertDetails}
      >
        {details}
      </Text>
    </div>
  );
};
