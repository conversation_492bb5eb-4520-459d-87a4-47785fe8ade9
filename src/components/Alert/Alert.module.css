.alertContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;

  background-color: white;
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.15);
}

.size__normal {
  max-width: 400px;
  padding: 24px 32px;
  border-radius: 12px;
  gap: 12px;
}
.size__small {
  max-width: 288px;
  padding: 16px 24px;
  border-radius: 8px;
  gap: 8px;
}

.darkContainer {
  background-color: var(--color-black);
  color: white;
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.3);
}

.darkContainer.type__warning .alertTitle,
.darkContainer.type__warning .alertIcon {
  color: var(--color-warning-light);
}
.darkContainer.type__failure .alertTitle,
.darkContainer.type__failure .alertIcon {
  color: #f76e64;
}
.darkContainer.type__success .alertTitle,
.darkContainer.type__success .alertIcon {
  color: #37d279;
}
.type__warning .alertIcon {
  color: var(--color-warning);
}
.type__failure .alertIcon {
  color: var(--color-failure);
}
.type__success .alertIcon {
  color: var(--color-green);
}

.alertTitle {
  text-align: center;
}

.alertDetails {
  text-align: center;
  color: var(--color-black-light);
}
.darkContainer .alertDetails {
  color: white;
}
