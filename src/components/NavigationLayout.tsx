import { Outlet } from 'react-router-dom';

import Slot from 'components/Slot';

import classNames from './NavigationLayout.module.css';

export const MainSidebarSlot = 'main-sidebar';
export const SidebarSlot = 'sidebar';

function NavigationLayout() {
  return (
    <main className={classNames.container}>
      <Slot id={MainSidebarSlot} exclusive />

      <div className={classNames.wrapper}>
        <div className={classNames.sidebar}>
          <Slot id={SidebarSlot} exclusive />
        </div>

        {/* We expect this to not render anything directly */}
        <Outlet />
      </div>
    </main>
  );
}

export default NavigationLayout;
