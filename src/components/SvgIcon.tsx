import { SVGProps, ForwardedRef, forwardRef, CSSProperties } from 'react';
import cx from 'classnames';

import classNames from './SvgIcon.module.css';

export type Props = SVGProps<SVGSVGElement> & {
  className?: string;
  style?: CSSProperties;
  name: string;
  prefix?: string;
  color?: string;
};

export const SvgIcon = forwardRef(
  (
    {
      name,
      prefix = 'icon',
      width = 24,
      height = 24,
      color,
      className,
      style,
    }: Props,
    ref: ForwardedRef<SVGSVGElement>,
  ) => {
    const symbolId = `#${prefix}-${name}`;

    return (
      <svg
        ref={ref}
        className={cx(classNames.icon, className)}
        style={style}
        width={width}
        height={height}
        color={color}
        aria-hidden="true"
      >
        <use href={symbolId} />
      </svg>
    );
  },
);

export default SvgIcon;
