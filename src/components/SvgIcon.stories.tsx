import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

import SvgIcon, { Props } from './SvgIcon';

import { IconNames } from 'constants/icons';

export default {
  title: 'Components/SvgIcon',
  component: SvgIcon,
  argTypes: {
    name: {
      options: IconNames,
      control: {
        type: 'select',
      },
    },
  },
  args: {
    name: 'Plus',
    color: 'black',
    width: 24,
    height: 24,
  },
} as Meta;

const Wrapper = styled.div`
  display: flex;
  gap: 16px;
  margin: 64px;
`;

export const Default: Story<Props> = (props) => (
  <Wrapper>
    <SvgIcon {...props} ref={null} />
  </Wrapper>
);

export const Large: Story<Props> = () => (
  <Wrapper>
    <SvgIcon name="Plus" width={64} height={64} />
    <SvgIcon name="StatusSuccess" width={64} height={64} />
  </Wrapper>
);

Large.parameters = {
  controls: {
    disable: true,
  },
};

export const Colored: Story<Props> = () => {
  const renderIcons = (color?: string) => (
    <div>
      <div>{color || 'unspecified'}</div>
      <Wrapper>
        {['Plus', 'Edit', 'Menu', 'StatusSuccess', 'StatusError'].map(
          (name) => (
            <SvgIcon key={name} name={name} color={color} />
          ),
        )}
      </Wrapper>
    </div>
  );

  return (
    <>
      {renderIcons(undefined)}
      {renderIcons('red')}
      {renderIcons('green')}
      {renderIcons('blue')}
    </>
  );
};

Colored.parameters = {
  controls: {
    disable: true,
  },
};
