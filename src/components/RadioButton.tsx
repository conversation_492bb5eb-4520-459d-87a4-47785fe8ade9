import { ComponentProps } from 'react';
import cx from 'classnames';

import classNames from './RadioButton.module.css';

type Props = ComponentProps<'input'> & {
  onSelect: () => void;
};
function RadioButton({ onSelect, className, ...otherProps }: Props) {
  return (
    <input
      type="radio"
      className={cx(classNames.radio, className)}
      onChange={(event) => {
        if (event.target.checked) {
          event.stopPropagation();
          onSelect();
        }
      }}
      {...otherProps}
    />
  );
}

export default RadioButton;
