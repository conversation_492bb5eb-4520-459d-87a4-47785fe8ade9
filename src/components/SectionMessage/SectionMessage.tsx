import React, { <PERSON> } from 'react';
import cx from 'classnames';

import SvgIcon from '../SvgIcon';

import classNames from './SectionMessage.module.css';

export type Props = {
  type: 'success' | 'warning' | 'failure';
  message: string;
  className?: string;
};

const icons = {
  success: {
    name: 'SuccessFilled',
    color: 'var(--color-green)',
  },
  warning: {
    name: 'WarningFilled',
    color: 'var(--color-warning)',
  },
  failure: {
    name: 'FailureFilled',
    color: 'var(--color-failure)',
  },
};

const SectionMessage: FC<Props> = ({ type, message, className }) => {
  const icon = icons[type];

  return (
    <div className={cx(classNames.sectionMessage, className)}>
      <div
        className={classNames.background}
        style={{ backgroundColor: icon.color }}
      />
      <SvgIcon
        className={classNames.icon}
        name={icon.name}
        color={icon.color}
        width={16}
        height={16}
      />
      <span className={classNames.content}>{message}</span>
    </div>
  );
};

export default SectionMessage;
