import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

import SectionMessage, { Props } from './SectionMessage';

export default {
  title: 'Components/SectionMessage',
  component: SectionMessage,
} as Meta;

const Wrapper = styled.div`
  margin: 32px;
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const text =
  'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Accusantium asperiores, consequuntur cum dolor earum.';

export const Default: Story<Props> = () => (
  <Wrapper>
    <SectionMessage type="failure" message={text} />
    <SectionMessage type="warning" message={text} />
    <SectionMessage type="success" message={text} />
  </Wrapper>
);
