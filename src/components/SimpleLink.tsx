import {
  ForwardedRef,
  forwardRef,
  DetailedHTMLProps,
  AnchorHTMLAttributes,
} from 'react';

const SimpleLink = forwardRef(
  (
    {
      children,
      onClick,
      ...otherProps
    }: DetailedHTMLProps<
      AnchorHTMLAttributes<HTMLAnchorElement>,
      HTMLAnchorElement
    >,
    ref: ForwardedRef<HTMLAnchorElement>,
  ) => (
    // eslint-disable-next-line jsx-a11y/anchor-is-valid
    <a
      href="#"
      ref={ref}
      onClick={(event) => {
        event.preventDefault();
        onClick?.(event);
      }}
      {...otherProps}
    >
      {children}
    </a>
  ),
);

export default SimpleLink;
