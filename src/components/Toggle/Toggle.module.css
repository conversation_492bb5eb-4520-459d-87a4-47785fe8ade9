.wrapper {
  background-color: var(--color-grey-10);
  border-radius: 8px;
  padding: 3px;
  color: var(--color-black);
  font-size: 14px;
  flex-grow: 1;
  display: flex;
}

.wrapper input[type='radio'] {
  box-sizing: border-box;
  padding: 0;
  position: absolute;
  left: -9999px;
  opacity: 0;
}

.content {
  position: relative;
  display: grid;
  flex-grow: 1;
  grid-template-columns: repeat(var(--item-count), 1fr);
}

.disabled {
  opacity: 0.6;
}

.disabled .label {
  cursor: not-allowed;
}

.group {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.active {
  border-radius: 5px;
  background-color: #fff;
  border: 1px solid var(--color-grey-30);
}

.label {
  cursor: pointer;
  position: relative;
  z-index: 2;
  display: flex;
  min-height: 32px;
  align-items: center;
  text-align: center;
  padding: 0 5px;
  user-select: none;
  font-weight: normal;
  flex-grow: 1;
}

.label__small {
  min-height: 34px;
}

.valueWrapper {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 18px;
  gap: 8px;
  white-space: pre;
  margin: 0 8px;
}

.value {
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;

  .active & {
    font-weight: 500;
  }
}

.disabled {
  opacity: 0.5;
}
