import { CSSProperties, useMemo } from 'react';
import { nanoid } from 'nanoid';
import cx from 'classnames';
import { Tooltip } from 'components/Tooltip/Tooltip';

import classNames from './Toggle.module.css';
import SvgIcon from 'components/SvgIcon';

export type Props = {
  className?: string;
  size?: 'normal' | 'small';
  options: Array<{
    key: string;
    value: string;
    icon?: string;
    tooltipText?: string;
  }>;
  disabled?: boolean;
  value: string;
  onChange: (key: any, option: { value: any }) => void;
};

const Toggle = ({
  className,
  size,
  options,
  value,
  onChange,
  disabled,
}: Props) => {
  const toggleId = useMemo(() => nanoid(), []);

  const activeIndex = useMemo(
    () => options.findIndex((option) => option.key === value),
    [options, value],
  );

  return (
    <div
      className={cx(
        classNames.wrapper,
        className,
        disabled && classNames.disabled,
      )}
    >
      <div
        className={classNames.content}
        style={{ '--item-count': options.length } as CSSProperties}
      >
        {options.map((option, index) => (
          <div
            className={cx(
              classNames.group,
              activeIndex === index && classNames.active,
            )}
            key={option.key}
          >
            <input
              disabled={disabled}
              checked={value === option.key}
              id={`${toggleId}-${index}`}
              type="radio"
              name={toggleId}
              onChange={(event) => {
                if (!event.target.checked || disabled) {
                  return;
                }
                onChange(option.key, option);
              }}
            />
            <Tooltip
              placement="top"
              disabled={!option?.tooltipText}
              content={option?.tooltipText}
            >
              <label
                className={cx(
                  classNames.label,
                  size === 'small' && classNames.label__small,
                )}
                htmlFor={`${toggleId}-${index}`}
              >
                <div className={classNames.valueWrapper}>
                  {option.icon && (
                    <SvgIcon name={option.icon} width={20} height={18} />
                  )}
                  <span className={classNames.value}>{option.value}</span>
                </div>
              </label>
            </Tooltip>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Toggle;
