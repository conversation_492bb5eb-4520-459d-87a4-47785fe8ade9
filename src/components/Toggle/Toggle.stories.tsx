import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';
import { useState } from 'react';

import ToggleExample, { Props } from './Toggle';

const TWO_OPTIONS = [
  { key: 'seeds_ha', value: 'seeds/ha' },
  { key: 'kg_ha', value: 'kg/ha' },
];

export default {
  title: 'Components/Toggle',
  component: ToggleExample,
  args: {
    options: TWO_OPTIONS,
    value: 'seeds_ha',
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  display: flex;
  margin-left: auto;
  margin-right: auto;
  margin-top: 50px;
`;

export const Toggle: Story<Props> = (args) => {
  const [val, setVal] = useState('seeds_ha');
  return (
    <Wrapper>
      <ToggleExample
        {...args}
        value={val}
        onChange={(value: string) => {
          setVal(value);
        }}
      />
    </Wrapper>
  );
};
