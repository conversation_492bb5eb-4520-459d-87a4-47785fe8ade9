.button {
  border: none;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  width: 100%;
  align-items: center;
  transition: opacity 0.3s, background-color 0.3s;
  cursor: pointer;
  position: relative;
  text-align: left;
}

.button:disabled {
  cursor: not-allowed;
}

/* SIZE */
.size__small {
  height: 32px;
  border-radius: 8px;
  padding: 0 8px;
}
.size__normal {
  height: 40px;
  border-radius: 8px;
  padding: 0 8px;
}

/* TEXT */
.text {
  font-size: 14px;
  line-height: 18px;
  padding: 0 4px;
  flex: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* THEME */
.theme__default {
  background-color: transparent;
  color: var(--color-black);
  border: 1px solid var(--color-grey-50);
  transition: border-color 0.3s, background-color 0.3s;
}
.theme__default:enabled:hover {
  border-color: var(--color-grey-70);
}
.theme__default:enabled:active,
.theme__default:enabled[aria-expanded='true'] {
  background-color: var(--color-grey-10);
  border-color: var(--color-grey-30);
}
