import { ReactNode, useState } from 'react';
import cx from 'classnames';

import Popover, { DefaultBox } from 'components/Popover/Popover';
import MapLegendButton from './MapLegendButton';

import classNames from './MapLegendPopover.module.css';

type Props = {
  children: ReactNode;
  isVisibleByDefault?: boolean;
};

const MapLegendPopover = ({ children, isVisibleByDefault = false }: Props) => {
  const [visible, setVisible] = useState(isVisibleByDefault);

  return (
    <Popover
      placement="top-start"
      offset={[0, 0]}
      visible={visible}
      interactive
      zIndex={9}
      render={(attrs) => (
        <DefaultBox theme="transparent" {...attrs}>
          <div className={cx(classNames.container, visible && 'legendActive')}>
            <div className={classNames.list}>{children}</div>
          </div>
        </DefaultBox>
      )}
      onShow={() => setVisible(true)}
      onHidden={() => setVisible(false)}
    >
      <MapLegendButton
        handleClick={() => setVisible(!visible)}
        isVisiblePopover={visible}
      />
    </Popover>
  );
};

export default MapLegendPopover;
