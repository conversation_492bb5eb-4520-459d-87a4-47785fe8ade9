.container {
  position: relative;
  width: 208px;
  padding: 8px 2px 8px 8px;
}

.container:before {
  background-color: black;
  height: calc(100% + 40px);
  width: 100%;
  top: 0;
  position: absolute;
  left: 0;
  z-index: -1;
  border-radius: 8px;
  mask-image: linear-gradient(
    to bottom,
    black calc(100% - 48px),
    transparent 100%
  );
}

.container:hover:before {
  content: '';
}

.list {
  max-height: 330px;
  overflow-y: auto;
  width: calc(100% - 6px);
  color: var(--color-grey-70);
}

.list::-webkit-scrollbar-track {
  background-color: transparent;
}

.list::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 9px;
}
.list::-webkit-scrollbar {
  width: 8px;
  background-color: transparent;
}
.list:hover::-webkit-scrollbar-thumb {
  background-color: var(--color-black-medium);
}
