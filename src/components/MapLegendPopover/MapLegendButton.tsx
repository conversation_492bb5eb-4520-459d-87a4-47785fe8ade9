import { ForwardedRef, forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import cx from 'classnames';

import Button from 'components/Button/Button';

import classNames from './MapLegendButton.module.css';

type ButtonProps = {
  isVisiblePopover: boolean;
  handleClick: () => void;
};

function MapLegendButton(
  { isVisiblePopover, handleClick }: ButtonProps,
  ref: ForwardedRef<HTMLButtonElement>,
) {
  const { t } = useTranslation();

  const filterText = isVisiblePopover
    ? t('map-legend.button.hide')
    : t('map-legend.button.show');

  const testId = isVisiblePopover ? '#legend_hide' : '#legend_show';

  return (
    <Button
      ref={ref}
      theme="map"
      className={cx(
        classNames.button,
        isVisiblePopover && classNames.legendButton__active,
        'legendButton',
      )}
      onClick={handleClick}
      data-testid={testId}
      textClassName={classNames.content}
      size="small"
      icon={isVisiblePopover ? 'Collapse' : 'ExpandLegend'}
      fitContent
    >
      <span>{filterText}</span>
    </Button>
  );
}

export default forwardRef(MapLegendButton);
