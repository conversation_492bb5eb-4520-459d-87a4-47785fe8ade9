.content {
  opacity: 1 !important;
  display: flex;
  align-items: center;
}
.button {
  background: transparent;
  box-shadow: none;
  color: white;
  position: relative;
  justify-content: flex-start;
  /* // Hack to override popover */
  z-index: 10;
}
.button:enabled:hover,
.button:hover,
.button:enabled[aria-expanded='true'],
.button:enabled[aria-expanded='true'] svg {
  background-color: transparent;
  opacity: 1 !important;
}

.legendButton__active {
  width: 200px;
}

:global(.legendButton:hover ~ div .legendActive::before) {
  content: '';
}
