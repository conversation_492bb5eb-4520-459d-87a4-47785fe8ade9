import { ReactNode } from 'react';

import SvgIcon from 'components/SvgIcon';

import classNames from './Dot.module.css';

export type Props = {
  color: string;
  title?: ReactNode;
};

const Dot = ({ color, title }: Props) => {
  return (
    <div className={classNames.container}>
      <SvgIcon name="CropColor" width={16} height={16} style={{ color }} />
      {!!title && <span>{title}</span>}
    </div>
  );
};

export default Dot;
