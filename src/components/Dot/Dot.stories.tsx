import { styled } from '@linaria/react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';

import DotExample, { Props } from './Dot';

export default {
  title: 'Components/Dot',
  component: DotExample,
  args: {
    color: 'var(--color-dot-green)',
    title: '90 kg/ha',
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <DotExample {...args} />
  </Wrapper>
);
