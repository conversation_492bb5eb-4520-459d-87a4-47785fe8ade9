.buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.group {
  display: flex;
  flex-direction: column;
}

.plus {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.minus {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

.between {
  background-color: var(--color-black);
}

.divider {
  height: 2px;
  width: 22px;
  margin: 0 auto;
  background: var(--color-black-medium);
  border-radius: 1px;
}

.rulerActive {
  background-color: var(--color-white);
  color: var(--color-black-light);
}
.rulerActive:enabled:hover {
  background-color: #ffffffe6;
}
.rulerActive:enabled:active,
.rulerActive:enabled[aria-expanded='true'] {
  background-color: #ffffffe6;
}
