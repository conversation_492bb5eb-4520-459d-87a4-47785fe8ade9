import { Meta, Story } from '@storybook/react';

import MapControls from './MapControls';
import { MapView } from 'components/MapView';
import PositionTo from 'components/PositionTo/PositionTo';

import { makeFieldSeason } from 'utils/mocks';

export default {
  title: 'Components/2 Map/MapControls',
  component: MapControls,
} as Meta;

export const ShowLocation: Story = () => (
  <MapView id="story">
    <PositionTo placement={'right center'} offsetX={15}>
      <MapControls showLocation />
    </PositionTo>
  </MapView>
);

export const ShowFieldLocation: Story = () => (
  <MapView id="story">
    <PositionTo placement={'right center'} offsetX={15}>
      <MapControls
        showFieldLocation={makeFieldSeason({
          bbox: [23.254581966, 55.185326837, 23.263620112, 55.189799838],
        })}
      />
    </PositionTo>
  </MapView>
);
