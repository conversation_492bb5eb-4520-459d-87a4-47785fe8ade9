import { useState } from 'react';
import { useStore } from 'effector-react';
import { BBox } from '@turf/helpers';
import cx from 'classnames';

import Button from 'components/Button/Button';

import { eventLogged } from 'models/analytics';
import { FieldSeason } from 'models/fieldSeasons';
import {
  LayerMode,
  layerMode$,
  setLayerMode,
  zoomedBy,
  zoomedToBbox,
  zoomedToPoint,
} from 'models/map';
import {
  isMapDrawModeEnabled$,
  mapDrawModeEnabledSet,
} from 'features/map-measure/models/map-measure';

import classNames from './MapControls.module.css';

const getPosition = (): Promise<GeolocationPosition> =>
  new Promise((resolve, reject) =>
    navigator.geolocation.getCurrentPosition(resolve, reject),
  );

type Props = {
  showLocation?: boolean;
  onZoomToPoint?: (data: {
    latitude: number;
    longitude: number;
    zoom: number;
  }) => void;
  onZoomBy?: (value: number) => void;
  showFieldLocation?: FieldSeason;
  onShowFieldLocation?: (data: { bbox: BBox; padding?: number }) => void;
  layerMode?: LayerMode;
  onSetLayerMode?: (value: LayerMode) => void;
  onSplit?: () => void;
  isSplit?: boolean;
  onRulerToggle?: (value: boolean) => void;
  rulerEnabled?: boolean;
  withoutRuler?: boolean;
};

const MapControls = ({
  showLocation,
  onZoomToPoint = zoomedToPoint,
  onShowFieldLocation = zoomedToBbox,
  onZoomBy = zoomedBy,
  onSetLayerMode = setLayerMode,
  showFieldLocation,
  onSplit,
  isSplit,
  onRulerToggle = mapDrawModeEnabledSet,
  withoutRuler,
  ...props
}: Props) => {
  const [positionPending, setPositionPending] = useState(false);
  const storeLayerMode = useStore(layerMode$);
  const layerMode = props.layerMode || storeLayerMode;
  const isMapDrawModeEnabled = useStore(isMapDrawModeEnabled$);
  const rulerEnabled = props.rulerEnabled || isMapDrawModeEnabled;

  return (
    <div className={classNames.buttons}>
      {onSplit && (
        <Button
          size="normal"
          icon="Split"
          theme="map"
          fitContent
          rounded
          data-testid="#map_split"
          onClick={() => {
            onSplit();
            eventLogged({
              name: 'yield_map_split',
              params: { mode: isSplit ? 'unsplit' : 'split' },
            });
          }}
        />
      )}
      {!withoutRuler && (
        <Button
          size="normal"
          icon="Ruler"
          className={cx(rulerEnabled && classNames.rulerActive)}
          theme="map"
          fitContent
          rounded
          data-testid="#map_ruler"
          onClick={() => {
            onRulerToggle(!rulerEnabled);
            eventLogged({
              name: 'yield_map_ruler',
              params: {},
            });
          }}
        />
      )}
      <Button
        size="normal"
        icon={layerMode === LayerMode.mode3D ? '2D' : '3D'}
        theme="map"
        fitContent
        rounded
        data-testid={layerMode === LayerMode.mode2D ? '#map_2D' : '#map_3D'}
        onClick={() =>
          onSetLayerMode(
            layerMode === LayerMode.mode2D
              ? LayerMode.mode3D
              : LayerMode.mode2D,
          )
        }
      />
      <div className={classNames.group}>
        <Button
          size="normal"
          icon="Plus"
          theme="map"
          fitContent
          className={classNames.plus}
          data-testid="#map_zoom_in"
          onClick={() => {
            onZoomBy(1);
            eventLogged({
              name: 'yield_map_zoom',
              params: { mode: 'in' },
            });
          }}
        />
        <div className={classNames.between}>
          <div className={classNames.divider} />
        </div>
        <Button
          size="normal"
          icon={'Minimize'}
          theme="map"
          fitContent
          className={classNames.minus}
          data-testid="#map_zoom_out"
          onClick={() => {
            onZoomBy(-1);
            eventLogged({
              name: 'yield_map_zoom',
              params: { mode: 'out' },
            });
          }}
        />
      </div>
      {!!showLocation && (
        <Button
          rounded
          size="normal"
          icon="Location"
          theme="map"
          fitContent
          data-testid="#map_zoom_to_user"
          loading={positionPending}
          onClick={() => {
            eventLogged({
              name: 'yield_map_zoom_to_user_location',
              params: {},
            });

            setPositionPending(true);
            getPosition()
              .then((position) => {
                onZoomToPoint({
                  longitude: position.coords.longitude,
                  latitude: position.coords.latitude,
                  zoom: 16,
                });
              })
              .finally(() => {
                setPositionPending(false);
              });
          }}
        />
      )}
      {!!showFieldLocation && (
        <Button
          rounded
          size="normal"
          iconSize={22}
          icon={'FieldLocation'}
          theme="map"
          fitContent
          data-testid="#map_zoom_to_field"
          onClick={() => {
            eventLogged({
              name: 'yield_map_zoom_to_field_location',
              params: {},
            });

            if (!showFieldLocation.bbox) {
              return;
            }
            onShowFieldLocation({ bbox: showFieldLocation.bbox, padding: 64 });
          }}
        />
      )}
    </div>
  );
};

export default MapControls;
