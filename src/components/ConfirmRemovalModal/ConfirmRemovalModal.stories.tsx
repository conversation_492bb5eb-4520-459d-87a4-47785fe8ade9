import { Meta, Story } from '@storybook/react';

import ConfirmRemovalModal, { Props } from './ConfirmRemovalModal';

export default {
  title: 'Components/ConfirmRemovalModal',
  component: ConfirmRemovalModal,
  argTypes: {
    itemTitle: { defaultValue: 'test thing here' },
    onCancel: { action: 'cancel' },
    onConfirm: { action: 'confirm' },
  },
} as Meta;

export const Default: Story<Props> = (args) => (
  <ConfirmRemovalModal {...args} />
);

export const ConfirmPending: Story<Props> = (args) => (
  <ConfirmRemovalModal {...args} confirmPending />
);
