import { useTranslation } from 'react-i18next';

import { ModalDeprecated, ModalHeader, ModalActions } from 'components/Modal';
import { Text } from 'components/Text';
import Button from 'components/Button/Button';

import classNames from './ConfirmRemovalModal.module.css';

export type Props = {
  itemTitle: string;
  subtitle?: string;
  confirmPending?: boolean;
  onCancel: () => void;
  onConfirm: () => void;
};

function ConfirmRemovalModal({
  itemTitle,
  subtitle,
  confirmPending,
  onCancel,
  onConfirm,
}: Props) {
  const { t } = useTranslation();

  return (
    <ModalDeprecated width={320} locked open>
      <ModalHeader>
        <Text size={16} weight="medium" align="center">
          {t('confirm-remove.title.v2', { item: itemTitle })}
        </Text>

        {subtitle && (
          <Text size={14} align="center" className={classNames.subtitle}>
            {subtitle}
          </Text>
        )}
      </ModalHeader>

      <ModalActions direction="vertical">
        <Button
          size="normal"
          theme="danger"
          loading={confirmPending}
          onClick={onConfirm}
        >
          {t('confirm-remove.confirm')}
        </Button>
        <Button size="normal" onClick={onCancel}>
          {t('confirm-remove.cancel')}
        </Button>
      </ModalActions>
    </ModalDeprecated>
  );
}

export default ConfirmRemovalModal;
