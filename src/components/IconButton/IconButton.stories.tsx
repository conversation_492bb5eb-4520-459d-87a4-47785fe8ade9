import IconButtonExample, { Props } from './IconButton';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

const EXAMPLE_ICONS = ['Profile', 'Plus'];

export default {
  title: 'Components/1 Buttons/IconButton',
  component: IconButtonExample,
  argTypes: {
    icon: {
      description: 'Sets button icon',
      options: EXAMPLE_ICONS,
      control: {
        type: 'select',
      },
    },
    iconSize: {
      description: 'Sets icon size',
      control: {
        type: 'range',
        min: 16,
        max: 48,
        step: 2,
      },
    },
    hitSlop: {
      description: 'Sets clickable area',
      control: {
        type: 'range',
        max: 12,
        step: 2,
      },
    },
    color: {
      description: 'Sets icon color',
      type: 'string',
    },
  },
  args: {
    icon: 'Profile',
    iconSize: 24,
    color: 'var(--color-grey-100)',
    hitSlop: 8,
  },
} as Meta;

const ButtonWrapper = styled.div`
  width: 200px;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<Props> = (args) => (
  <ButtonWrapper>
    <IconButtonExample {...args} />
  </ButtonWrapper>
);
