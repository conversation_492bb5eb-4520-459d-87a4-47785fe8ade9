import { ForwardedRef, MouseEvent, forwardRef, CSSProperties } from 'react';
import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';

import classNames from './IconButton.module.css';

export type Props = {
  className?: string;
  dataTestId?: string;
  icon: string;
  iconSize?: number;
  color?: string;
  hoverColor?: string;
  hitSlop?: number;
  onClick?: (event: MouseEvent) => void;
  disabled?: boolean;
};

const IconButton = (
  {
    className,
    dataTestId,
    icon,
    iconSize = 24,
    color,
    hoverColor = 'var(--color-primary)',
    hitSlop = 8,
    onClick,
    disabled,
    ...otherProps
  }: Props,
  ref: ForwardedRef<HTMLButtonElement>,
) => (
  <button
    ref={ref}
    className={cx(classNames.button, className)}
    data-testid={dataTestId}
    style={
      {
        '--color-base': color,
        '--color-hover': hoverColor,
        '--size-hit-slop': `${hitSlop}px`,
        '--size-icon': `${iconSize}px`,
      } as CSSProperties
    }
    onClick={onClick}
    disabled={disabled}
    {...otherProps}
  >
    <SvgIcon name={icon} width={iconSize} height={iconSize} />
  </button>
);

export default forwardRef(IconButton);
