.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  position: relative;
  color: var(--color-base);
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.button:disabled:hover {
  color: var(--color-base);
}

.button:hover,
.button:active,
.button[aria-expanded='true'] {
  color: var(--color-hover);
}

.button:before {
  content: '';
  width: calc(100% + var(--size-hit-slop));
  height: calc(100% + var(--size-hit-slop));
  position: absolute;
}

.button:disabled {
  cursor: default;
  opacity: 0.5;
  pointer-events: none;
}
