.container {
  height: 100%;
  min-height: 600px;
  position: relative;
  display: flex;
  background-color: var(--color-grey-30);
}

.wrapper {
  display: flex;
  border-radius: 8px 0 0 8px;
  width: 100%;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.15),
    0px 0px 2px 0px rgba(0, 0, 0, 0.1);
}

.sidebar {
  display: flex;
  border-radius: 8px 0 0 8px;
  background: white;
  height: 100%;
  position: relative;
  flex-shrink: 0;
  z-index: 3;
}
