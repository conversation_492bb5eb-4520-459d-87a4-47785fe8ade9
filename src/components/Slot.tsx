import { Fragment, ReactNode } from 'react';
import { useStoreMap } from 'effector-react';

import { slotsContent$ } from 'models/slots';

type Props = {
  id: string;
  initialContent?: ReactNode;
  exclusive?: boolean;
};

function Slot({ id, initialContent, exclusive }: Props) {
  const items = useStoreMap(slotsContent$, (content) => content[id]);

  if (!items || items.length === 0) {
    return <>{initialContent}</>;
  }

  if (exclusive) {
    return <>{items[items.length - 1]!.element}</>;
  }

  return (
    <>
      {items.map((item) => (
        <Fragment key={item.itemId}>{item.element}</Fragment>
      ))}
    </>
  );
}

export default Slot;
