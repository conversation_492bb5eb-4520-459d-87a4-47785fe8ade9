.container {
  flex-shrink: 0;
  padding: 16px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.title {
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  white-space: nowrap;
}

.subtitle {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: var(--color-black-light);
  overflow: hidden;
  text-overflow: ellipsis;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content {
  display: flex;
  flex-direction: column;
}
