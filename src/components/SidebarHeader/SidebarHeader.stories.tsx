import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';
import { action } from '@storybook/addon-actions';

import SidebarHeader, { Props } from './SidebarHeader';
import NavButton from '../NavButton/NavButton';
import Button from 'components/Button/Button';
import GuideLink from 'components/GuideLink';

export default {
  title: 'Components/SidebarHeader',
  component: SidebarHeader,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;
const Row = styled.div`
  display: flex;
  gap: 8px;
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <SidebarHeader title="VRA map">
      <NavButton onClick={action('report-clicked')}>Back</NavButton>
    </SidebarHeader>
    <SidebarHeader title="VRA map" subtitle="05kl, 164,7 ha">
      <NavButton onClick={action('report-clicked')}>Back</NavButton>
      <Row>
        <GuideLink url="http://google.com">User guide</GuideLink>
        <Button
          icon="Kebab"
          size="small"
          theme="white"
          onClick={action('kebab-clicked')}
        />
      </Row>
    </SidebarHeader>
  </Wrapper>
);
