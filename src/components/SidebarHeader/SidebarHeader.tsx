import { ReactNode } from 'react';

import classNames from './SidebarHeader.module.css';

export type Props = {
  title: string;
  subtitle?: string;
  children?: ReactNode;
};

const SidebarHeader = ({ title, subtitle, children }: Props) => {
  return (
    <div className={classNames.container}>
      <div className={classNames.header}>{children}</div>
      <div className={classNames.content}>
        <span className={classNames.title}>{title}</span>
        {subtitle && <span className={classNames.subtitle}>{subtitle}</span>}
      </div>
    </div>
  );
};

export default SidebarHeader;
