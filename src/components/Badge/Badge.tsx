import cx from 'classnames';
import SvgIcon from 'components/SvgIcon';

import classNames from './Badge.module.css';

type BadgeProps = {
  children: string;
  icon?: string;
  className?: string;
};

export const Badge = ({ children, icon, className }: BadgeProps) => {
  return (
    <div className={cx(classNames.badge, className)}>
      {!!icon && <SvgIcon name={icon} width={16} height={16} />}
      <div className={classNames.text}>{children}</div>
    </div>
  );
};
