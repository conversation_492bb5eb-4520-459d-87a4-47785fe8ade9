import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import { LinkButtonProps } from './types';
import classNames from './LinkButton.module.css';

const LinkButton = ({
  children,
  className,
  icon,
  onClick,
}: LinkButtonProps) => {
  return (
    <button className={cx(classNames.button, className)} onClick={onClick}>
      {!!icon && <SvgIcon name={icon} width={16} height={16} />}
      <span className={classNames.text}>{children}</span>
    </button>
  );
};

export default LinkButton;
