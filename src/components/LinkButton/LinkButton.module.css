.button {
  border: none;
  background-color: transparent;
  padding: 0;
  display: inline-flex;
  gap: 5px;
  align-items: center;
  transition: color 0.3s;
  position: relative;
  cursor: pointer;

  color: var(--color-primary);
}

.button:hover {
  color: var(--color-secondary);
}

.text {
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
  transition: opacity 0.3s;
}

.button:active .text {
  opacity: 0.5;
}
