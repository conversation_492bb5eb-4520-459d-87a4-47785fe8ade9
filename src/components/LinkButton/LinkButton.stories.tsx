import LinkButtonExample from './LinkButton';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';
import { LinkButtonProps } from './types';

export default {
  title: 'Components/1 Buttons/LinkButton',
  component: LinkButtonExample,
  argTypes: {
    children: { defaultValue: 'Кнопка' },
    onClick: { action: 'clicked' },
  },
} as Meta;

const ButtonWrapper = styled.div`
  width: 200px;
  margin-left: auto;
  margin-right: auto;
`;

export const LinkButton: Story<LinkButtonProps> = (args) => (
  <ButtonWrapper>
    <LinkButtonExample {...args} />
  </ButtonWrapper>
);
