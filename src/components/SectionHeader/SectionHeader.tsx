import { ReactNode } from 'react';
import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import Checkbox from 'components/Checkbox/Checkbox/Checkbox';

import classNames from './SectionHeader.module.css';

export type SectionHeaderProps = {
  title: string;
  subtitle?: string;
  area?: string;
  right?: ReactNode;
  canCollapse?: boolean;
  collapsed?: boolean;
  canSelectAll?: boolean;
  selectedAll?: boolean;
  children?: ReactNode;
  onClick?: () => void;
  onSelectAll?: () => void;
};

export const SectionHeader = ({
  title,
  subtitle,
  area,
  right,
  canCollapse,
  collapsed,
  canSelectAll,
  selectedAll,
  children,
  onClick,
  onSelectAll,
}: SectionHeaderProps) => {
  return (
    <section>
      <div
        className={cx(
          classNames.sectionHeader,
          !canCollapse && classNames.sectionHeaderNotCollapsible,
        )}
      >
        <div
          className={cx(
            classNames.listHeader,
            canCollapse && classNames.listHeaderCollapsible,
          )}
          onClick={() => {
            if (canCollapse) {
              onClick?.();
            }
          }}
        >
          {canCollapse && (
            <div className={classNames.chevron}>
              <SvgIcon
                name={collapsed ? 'ChevronRight' : 'ChevronDown'}
                width={16}
                height={16}
              />
            </div>
          )}
          <div className={classNames.text}>
            <div className={classNames.title}>{title}</div>
            {subtitle && <div className={classNames.subtitle}>{subtitle}</div>}
          </div>
        </div>

        <div className={classNames.sectionHeaderRight}>
          {!!area && <span className={classNames.area}>{area}</span>}

          {canSelectAll && (
            <Checkbox
              className={classNames.checkbox}
              checked={selectedAll}
              onChange={onSelectAll}
              revert
            />
          )}

          {right}
        </div>
      </div>
      {!collapsed && children}
    </section>
  );
};
