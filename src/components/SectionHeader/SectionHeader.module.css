.sectionHeader {
  height: 57px;
  padding: 8px 16px;
  background-color: white;
  border-top: 1px solid var(--color-grey-10);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.sectionHeaderNotCollapsible {
  height: initial;
  padding: 16px 16px 8px;
}

.listHeader {
  display: flex;
  align-items: center;
  min-width: 0;
}

.listHeaderCollapsible {
  cursor: pointer;
}

.chevron {
  padding: 8px 8px 8px 0;
}

.sectionHeaderRight {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.area {
  font-size: 12px;
  line-height: 16px;
  font-weight: normal;
  flex-shrink: 0;
  color: var(--color-black-light);
}

.checkbox {
  padding: 3px;
}

.text {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 4px 0;
}

.title {
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.subtitle {
  font-size: 12px;
  line-height: 16px;
  color: var(--color-black-light);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
