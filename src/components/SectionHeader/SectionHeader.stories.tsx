import { useState } from 'react';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

import { SectionHeader, SectionHeaderProps } from './SectionHeader';

export default {
  title: 'Components/SectionHeader',
  component: SectionHeader,
} as Meta;

const Wrapper = styled.div`
  margin: 32px;
  width: 265px;
`;

export const Default: Story<SectionHeaderProps> = () => {
  const [state, setState] = useState({
    Collapsible: false,
    WithArea: true,
    CanSelectAll: true,
    Long: true,
  });
  const [allSelected, setAllSelected] = useState(false);

  return (
    <Wrapper>
      <SectionHeader title="Section Header">content</SectionHeader>
      <SectionHeader title="Section Header" subtitle="Subtitle">
        content
      </SectionHeader>
      <SectionHeader
        title="Collapsible"
        onClick={() => {
          setState((prev) => ({ ...prev, Collapsible: !prev.Collapsible }));
        }}
        canCollapse
        collapsed={state.Collapsible}
      >
        content
      </SectionHeader>
      <SectionHeader
        title="With area"
        onClick={() => {
          setState((prev) => ({ ...prev, WithArea: !prev.WithArea }));
        }}
        canCollapse
        collapsed={state.WithArea}
        area="10 ha"
      >
        content
      </SectionHeader>
      <SectionHeader
        title="Can select all"
        onClick={() => {
          setState((prev) => ({ ...prev, CanSelectAll: !prev.CanSelectAll }));
        }}
        canCollapse
        collapsed={state.CanSelectAll}
        area="10 ha"
        canSelectAll
        selectedAll={allSelected}
        onSelectAll={() => {
          setAllSelected((prev) => !prev);
        }}
      >
        content
      </SectionHeader>
      <SectionHeader
        title="Long Enought Title"
        subtitle="Long Enought Subtitle"
        onClick={() => {
          setState((prev) => ({ ...prev, Long: !prev.Long }));
        }}
        canCollapse
        collapsed={state.Long}
        area="5000 ha"
        canSelectAll
        selectedAll={allSelected}
        onSelectAll={() => {
          setAllSelected((prev) => !prev);
        }}
      >
        content
      </SectionHeader>
    </Wrapper>
  );
};
