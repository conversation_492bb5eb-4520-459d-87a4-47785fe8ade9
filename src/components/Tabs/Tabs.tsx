import React, { createContext, FC, ReactElement, useContext } from 'react';
import cx from 'classnames';

import { TabPanelProps, TabProps, TabsContextProps, TabsProps } from './types';
import classNames from './Tabs.module.css';

export const TabsContext = createContext<TabsContextProps>({
  activeIndex: 0,
  setActiveIndex: () => {},
});

export const TabList = ({ children }: { children: ReactElement[] }) => {
  if (!children) return null;
  return (
    <ul className={classNames.list}>
      {React.Children.map(children, (child: ReactElement, index: number) =>
        React.cloneElement(child as React.ReactElement<any>, {
          tabIndex: index,
        }),
      )}
    </ul>
  );
};

export const Tabs = ({ children, activeIndex, setActiveIndex }: TabsProps) => {
  return (
    <TabsContext.Provider value={{ activeIndex, setActiveIndex }}>
      <div className={classNames.tabs}>{children}</div>
    </TabsContext.Provider>
  );
};

export const Tab = ({ children, onClick, tabIndex, disabled }: TabProps) => {
  const tabContext = useContext<TabsContextProps>(TabsContext);
  const isActive = tabIndex === tabContext.activeIndex;
  return (
    <li className={classNames.item}>
      <button
        onClick={() => {
          if (onClick) {
            onClick(tabIndex);
            return;
          }
          if (isActive) {
            return;
          }
          tabContext.setActiveIndex(tabIndex!);
        }}
        className={cx(
          classNames.button,
          isActive && classNames.button__active,
          disabled && classNames.button__disabled,
        )}
      >
        {children}
      </button>
    </li>
  );
};

export const TabPanelList: FC = ({ children }) => {
  if (children)
    return (
      <>
        {React.Children.map(children, (child, index) =>
          React.cloneElement(child as React.ReactElement<any>, {
            panelIndex: index,
          }),
        )}
      </>
    );
  return null;
};

export const TabPanel = ({ children, panelIndex }: TabPanelProps) => {
  const { activeIndex } = useContext<TabsContextProps>(TabsContext);

  if (activeIndex === panelIndex)
    return <div className={classNames.panel}>{children}</div>;
  return null;
};
