import { useState } from 'react';
import { Meta, Story } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { styled } from '@linaria/react';

import { Tab, TabList, TabPanel, TabPanelList, Tabs } from './Tabs';
import { TabsProps } from './types';

export default {
  title: 'Components/Tabs',
  component: Tabs,
  subcomponents: {
    Tab,
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<TabsProps> = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const handleClick = action('tab-click');

  return (
    <Wrapper>
      <Tabs activeIndex={activeIndex} setActiveIndex={setActiveIndex}>
        <TabList>
          <Tab onClick={handleClick}>Long Variant</Tab>
          <Tab onClick={handleClick}>Short Variant</Tab>
        </TabList>
        <TabPanelList>
          <TabPanel>
            <div>
              <p>
                When doing variable-rate seeding, we factor in the variability
                within a field and sow different amounts of seeds in different
                areas accordingly. We increase the rate in high-productivity
                areas and decrease it in low-productivity areas. This allows us
                to increase yield, save on seeds, and reduce time working with
                machinery.
              </p>

              <h2>How to create a map for variable-rate seeding</h2>
              <p>
                You can do this in the OneSoil web app. If you already have a
                mobile app account, just sign into the web app using the same
                email and password.
              </p>
              <p>
                1️⃣ Open the OneSoil web app on your computer and go to the
                'Sowing Rate' tab.
              </p>
              <p>
                2️⃣ Select the field you want to create a map for and wait a
                couple of seconds.
              </p>
              <p>
                3️⃣ Indicate the crops that grew in this field in previous years.
                This improves the accuracy of productivity zone mapping. We
                divide the field into three productivity zones, with each one
                getting its own seeding rate.
              </p>
              <p>
                If your field was fallow one year, ignore that year when
                building productivity zones.
              </p>
              <p>
                4️⃣ Enter sowing rates. You can use either kilograms or seeds per
                hectare (or pounds and seeds per acre). We recommend setting
                rates with at least a difference of 10,000 seeds per hectare
                between them. Otherwise, you won't notice any change in the
                yield.
              </p>
              <p>
                5️⃣ Last but not least, select the onboard computer type and
                download the file.
              </p>
            </div>
          </TabPanel>
          <TabPanel>
            <p>
              Sometimes you walk around the field, and it goes on as far as the
              eye can see. How do you scout crops efficiently if you have a huge
              field? In this section, I'll walk you through how the OneSoil apps
              can help you prepare and conduct field scouting. It won't help you
              get in your steps, but you'll find problem areas a lot faster.
            </p>
          </TabPanel>
        </TabPanelList>
      </Tabs>
    </Wrapper>
  );
};
