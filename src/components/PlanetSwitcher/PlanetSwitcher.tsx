import Button from 'components/Button/Button';
import MenuLink from 'components/MenuLink';
import Popover, { usePopover } from 'components/Popover/Popover';
import SvgIcon from 'components/SvgIcon';

import classNames from './PlanetSwitcher.module.css';
import { useTranslation } from 'react-i18next';

type Props = {
  mode?: 'dark' | 'light';
  value: boolean;
  onChange: (value: boolean) => void;
};

const Content = ({ value, onChange }: Props) => {
  const { t } = useTranslation();
  const { hide } = usePopover();
  return (
    <ul className={classNames.menu}>
      <li>
        <MenuLink
          className={value ? classNames.active : ''}
          onClick={() => {
            onChange(true);
            hide();
          }}
        >
          <div className={classNames.menuItem}>
            <div className={classNames.itemTitle}>
              {t('satellite-source.planet.title')}
            </div>
            <div className={classNames.itemDescription}>
              {t('satellite-source.planet.description')}
            </div>
          </div>
        </MenuLink>
      </li>
      <li>
        <MenuLink
          className={!value ? classNames.active : ''}
          onClick={() => {
            onChange(false);
            hide();
          }}
        >
          <div className={classNames.menuItem}>
            <div className={classNames.itemTitle}>
              {t('satellite-source.sentinel.title')}
            </div>
            <div className={classNames.itemDescription}>
              {t('satellite-source.sentinel.description')}
            </div>
          </div>
        </MenuLink>
      </li>
    </ul>
  );
};

export const PlanetSwitcher = ({ mode = 'light', value, onChange }: Props) => {
  const { t } = useTranslation();
  const btnTitle = value
    ? t('satellite-source.planet.title')
    : t('satellite-source.sentinel.title');
  return (
    <Popover
      trigger="click"
      placement="bottom"
      content={<Content value={value} onChange={onChange} />}
      interactive
    >
      <div>
        <Button
          icon="Expand"
          iconPosition="right"
          theme={mode === 'dark' ? 'black' : 'white'}
          rounded
          size="normal"
          className={classNames.switcherBtn}
        >
          <SvgIcon
            name="Satellite"
            color={
              mode === 'dark' ? 'var(--color-white)' : 'var(--color-black)'
            }
          />
          <span>{btnTitle}</span>
        </Button>
      </div>
    </Popover>
  );
};
