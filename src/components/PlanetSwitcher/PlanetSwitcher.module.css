.menu {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.menu .active {
  background-color: var(--color-grey-10);
}

.active .itemTitle {
  font-weight: 500;
}

.menuItem {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  border-radius: 4px;
}

.itemTitle {
  font-size: 14px;
}

.itemDescription {
  font-size: 12px;
  color: var(--color-black-light);
}

.switcherBtn {
  display: flex;
  align-items: center;
  width: 100%;
}

.switcherBtn > div {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 8px;
}
