import { <PERSON>a, Story } from '@storybook/react';

import Button from 'components/Button/Button';
import { Modal, ModalTitle, ModalText, ModalButtons } from 'components/Modal';
import { ModalProps, ModalSize } from './types';

export default {
  title: 'Components/Modal',
  component: Modal,
} as Meta;

export const Default: Story<ModalProps> = () => (
  <Modal size={ModalSize.Small} open>
    <ModalTitle>Reset settings?</ModalTitle>

    <ModalText>
      This will reset your App preferences back to their default settings.
      Connected accounts will also be signed out.
    </ModalText>

    <ModalButtons>
      <Button size="normal" icon="Cross" fitContent>
        Cancel
      </Button>
      <Button size="normal" icon="CheckMark" theme="black" fitContent>
        Confirm
      </Button>
    </ModalButtons>
  </Modal>
);
