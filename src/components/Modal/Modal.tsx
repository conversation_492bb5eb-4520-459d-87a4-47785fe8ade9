import 'wicg-inert';

import { ReactNode, Fragment, useRef, useState, useEffect, FC } from 'react';
import cx from 'classnames';

import { Portal } from '../Portal';
import { Text } from 'components/Text';
import IconButton from 'components/IconButton/IconButton';
import DocumentKeyHandler from 'components/DocumentKeyHandler/DocumentKeyHandler';
import { ModalProps, ModalSize } from './types';

import classNames from './Modal.module.css';

type WrapperProps = {
  mode: string;
  noBackdrop?: boolean;
  wrapperClassname?: string;
  children: ReactNode;
};

type TitleProps = {
  children?: ReactNode;
  className?: string;
  weight?: 'medium';
};

type SectionProps = {
  disabled?: boolean;
};

type TextProps = {
  children?: ReactNode;
};

type ButtonsProps = {
  justifyContent?: 'flexEnd' | 'spaceBetween';
  children?: ReactNode;
};

export const ModalTitle = ({ children }: TitleProps) => (
  <Text size={18} weight="medium">
    {children}
  </Text>
);

export const ModalSection: FC<SectionProps> = ({ disabled, children }) => (
  <div
    className={cx(
      classNames.modalSection,
      disabled && classNames.modalSectionDisabled,
    )}
  >
    {children}
  </div>
);

export const ModalSectionTitle = ({ children, weight }: TitleProps) => (
  <Text weight={weight} size={14}>
    {children}
  </Text>
);

export const ModalText = ({ children }: TextProps) => (
  <Text size={14} isParagraph className={classNames.text}>
    {children}
  </Text>
);

export const ModalButtons = ({
  justifyContent = 'flexEnd',
  children,
}: ButtonsProps) => (
  <div
    className={cx(
      classNames.buttons,
      justifyContent === 'spaceBetween' && classNames.buttonsSpaceBetween,
    )}
  >
    {children}
  </div>
);

export const ModalDivider = () => {
  return <div className={classNames.divider} />;
};

const Wrapper = ({
  mode,
  noBackdrop,
  wrapperClassname,
  children,
}: WrapperProps) => {
  if (mode === 'relative') {
    if (noBackdrop) {
      return (
        <div className={cx(classNames.wrapperNoBackdrop, wrapperClassname)}>
          {children}
        </div>
      );
    }
    return (
      <div className={cx(classNames.wrapper, wrapperClassname)}>{children}</div>
    );
  }
  return <Portal className="modal-portal">{children}</Portal>;
};

export const Modal = ({
  width,
  size = ModalSize.Default,
  height,
  open,
  showCloseButton,
  onShow,
  onClose,
  onSubmit,
  locked,
  children,
  className,
  wrapperClassname,
  noBackdrop = false,
  mode = 'absolute',
}: ModalProps) => {
  const [active, setActive] = useState(false);
  const backdrop = useRef<HTMLDivElement>(null);

  // Keep stable identity for close, to avoid triggering effect below too often
  const close = useRef<ModalProps['onClose']>(onClose);
  close.current = onClose;

  useEffect(() => {
    const { current } = backdrop;

    const transitionEnd = (e: TransitionEvent) => {
      setActive(open);

      // many animation triggers this, we need one
      if (e.propertyName === 'transform') {
        onShow?.();
      }
    };

    const keyHandler = (e: KeyboardEvent) =>
      !locked && [27].indexOf(e.which) >= 0 && close.current?.();

    const clickHandler: EventListener = (e) =>
      !locked && e.target === current && close.current?.();

    let openTimer: number;

    if (current) {
      current.addEventListener('transitionend', transitionEnd);
      current.addEventListener('click', clickHandler);
      window.addEventListener('keyup', keyHandler);
    }

    if (open) {
      openTimer = window.setTimeout(() => {
        setActive(open);
        if (mode === 'absolute') {
          document.querySelector('#root')?.setAttribute('inert', 'true');
        }
      }, 10);
    }

    return () => {
      if (current) {
        current.removeEventListener('transitionend', transitionEnd);
        current.removeEventListener('click', clickHandler);
      }

      if (mode === 'absolute') {
        document.querySelector('#root')?.removeAttribute('inert');
      }
      window.removeEventListener('keyup', keyHandler);
      clearTimeout(openTimer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, locked, mode]);

  return (
    <Fragment>
      {(open || active) && (
        <Wrapper
          mode={mode}
          noBackdrop={noBackdrop}
          wrapperClassname={wrapperClassname}
        >
          <div
            ref={backdrop}
            className={cx(
              classNames.backdrop,
              active && open && classNames.backdrop__active,
              noBackdrop && classNames.backdrop__disabled,
              // for scroll under modal
              mode === 'relative' && noBackdrop && classNames.backdrop__off,
            )}
          >
            {onSubmit && (
              <DocumentKeyHandler hotkey="Enter" onPress={onSubmit} />
            )}
            <div
              className={cx(
                classNames.container,
                classNames[`size__${size}`],
                className,
              )}
              style={{ width, height }}
            >
              {children}
              {showCloseButton && (
                <IconButton
                  hoverColor="var(--color-black)"
                  icon={'Cross'}
                  onClick={() => onClose?.()}
                  className={classNames.close}
                />
              )}
            </div>
          </div>
        </Wrapper>
      )}
    </Fragment>
  );
};
