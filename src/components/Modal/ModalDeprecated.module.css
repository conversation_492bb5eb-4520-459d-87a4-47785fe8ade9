.backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;

  background-color: rgba(51, 51, 51, 0.3);
  pointer-events: all;
  backdrop-filter: blur(1px);

  opacity: 0;
  transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.backdrop__disabled {
  background-color: transparent;
  pointer-events: none;
}

.backdrop__active {
  opacity: 1;
}

.header {
  margin: 24px;
  display: flex;
  flex-direction: column;
}
.headerCenter {
  align-items: center;
}
.headerWithClose {
  margin-right: 56px;
}

.container {
  position: relative;
  box-sizing: border-box;
  min-height: 50px;
  min-width: 50px;
  max-height: calc(100vh - 10px);
  max-width: calc(100vw - 80px);
  box-shadow: 0 6px 12px 0 #00000033;
  background-color: white;
  border-radius: 16px;
  pointer-events: all;

  transform: translateY(50px);
  opacity: 0;
  transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.backdrop__active .container {
  transform: translateY(0);
  opacity: 1;
}

.content {
  margin: 24px;
}

.actions {
  margin: 24px;
  display: flex;
  flex-direction: row;
  gap: 12px;
}
.actionsVertical {
  flex-direction: column;
}
.actionsCenter {
  justify-content: center;
}

.actionsRight {
  justify-content: flex-end;
}

.close {
  position: absolute;
  right: 16px;
  top: 16px;
}
