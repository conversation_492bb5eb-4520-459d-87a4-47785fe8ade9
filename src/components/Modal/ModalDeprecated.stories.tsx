import { Meta, Story } from '@storybook/react';

import Button from 'components/Button/Button';
import { Text } from 'components/Text';
import TextInput from 'components/TextInput/TextInput';
import {
  ModalDeprecated,
  ModalHeader,
  ModalContent,
  ModalActions,
} from 'components/Modal';
import { ModalProps } from './types';

export default {
  title: 'Components/ModalDeprecated',
  component: ModalDeprecated,
} as Meta;

export const Horizontal: Story<ModalProps> = () => (
  <ModalDeprecated width={400} open showCloseButton>
    <ModalHeader>
      <Text size={24} weight="medium">
        Title
      </Text>
    </ModalHeader>

    <ModalContent>
      <TextInput />
    </ModalContent>

    <ModalActions>
      <Button size="large">Cancel</Button>
      <Button size="large" theme="primary">
        Analyze
      </Button>
    </ModalActions>
  </ModalDeprecated>
);

export const Vertical: Story<ModalProps> = () => (
  <ModalDeprecated width={400} open>
    <ModalHeader>
      <Text size={24} weight="medium">
        Title
      </Text>
    </ModalHeader>

    <ModalContent>
      <TextInput />
    </ModalContent>

    <ModalActions direction="vertical">
      <Button size="normal" theme="primary">
        Exit without saving
      </Button>
      <Button size="normal">Back to edit</Button>
    </ModalActions>
  </ModalDeprecated>
);

export const WithoutContent: Story<ModalProps> = () => (
  <ModalDeprecated width={320} open locked>
    <ModalHeader>
      <Text size={16} weight="medium" align="center">
        Are you sure you want to exit this from without saving?
      </Text>
    </ModalHeader>

    <ModalActions direction="vertical">
      <Button size="normal" theme="primary">
        Exit without saving
      </Button>
      <Button size="normal">Back to edit</Button>
    </ModalActions>
  </ModalDeprecated>
);

export const SingleButton: Story<ModalProps> = () => (
  <ModalDeprecated width={320} open locked>
    <ModalHeader>
      <Text size={16} weight="medium" align="center">
        You've reached the limit for the current subscription
      </Text>
    </ModalHeader>

    <ModalActions align="center">
      <Button size="normal" theme="primary" fitContent>
        Analyze
      </Button>
    </ModalActions>
  </ModalDeprecated>
);
