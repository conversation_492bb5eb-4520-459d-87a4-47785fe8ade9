import 'wicg-inert';

import { ReactNode, Fragment, useRef, useState, useEffect } from 'react';
import cx from 'classnames';

import { Portal } from '../Portal';
import Button from 'components/Button/Button';
import { ModalDeprecatedProps } from './types';

import classNames from './ModalDeprecated.module.css';

type HeaderProps = {
  children?: ReactNode;
  showCloseButton?: boolean;
  align?: 'center';
};

type ContentProps = {
  children?: ReactNode;
};

type ActionsProps = {
  children?: ReactNode;
  direction?: 'horizontal' | 'vertical';
  align?: 'center' | 'right';
};

// deprecated
export const ModalHeader = ({
  children,
  align,
  showCloseButton,
}: HeaderProps) => {
  return (
    <div
      className={cx(
        classNames.header,
        align === 'center' && classNames.headerCenter,
        showCloseButton && classNames.headerWithClose,
      )}
    >
      {children}
    </div>
  );
};

// deprecated
export const ModalContent = ({ children }: ContentProps) => {
  return <div className={classNames.content}>{children}</div>;
};

export const ModalActions = ({
  children,
  align,
  direction = 'horizontal',
}: ActionsProps) => {
  return (
    <div
      className={cx(
        classNames.actions,
        direction === 'vertical' && classNames.actionsVertical,
        align === 'center' && classNames.actionsCenter,
        align === 'right' && classNames.actionsRight,
      )}
    >
      {children}
    </div>
  );
};

/**
 * @deprecated
 */
export const ModalDeprecated = ({
  width,
  open,
  showCloseButton,
  onClose,
  locked,
  children,
  noBackdrop = false,
}: ModalDeprecatedProps) => {
  const [active, setActive] = useState(false);
  const backdrop = useRef<HTMLDivElement>(null);

  // Keep stable identity for close, to avoid triggering effect below too often
  const close = useRef<ModalDeprecatedProps['onClose']>(onClose);
  close.current = onClose;

  useEffect(() => {
    const { current } = backdrop;

    const transitionEnd = () => setActive(open);

    const keyHandler = (e: KeyboardEvent) =>
      !locked && [27].indexOf(e.which) >= 0 && close.current?.();

    const clickHandler: EventListener = (e) =>
      !locked && e.target === current && close.current?.();

    let openTimer: number;

    if (current) {
      current.addEventListener('transitionend', transitionEnd);
      current.addEventListener('click', clickHandler);
      window.addEventListener('keyup', keyHandler);
    }

    if (open) {
      openTimer = window.setTimeout(() => {
        (document.activeElement as HTMLElement)?.blur();
        setActive(open);
        document.querySelector('#root')?.setAttribute('inert', 'true');
      }, 10);
    }

    return () => {
      if (current) {
        current.removeEventListener('transitionend', transitionEnd);
        current.removeEventListener('click', clickHandler);
      }

      document.querySelector('#root')?.removeAttribute('inert');
      window.removeEventListener('keyup', keyHandler);
      clearTimeout(openTimer);
    };
  }, [open, locked]);

  return (
    <Fragment>
      {(open || active) && (
        <Portal className="modal-portal">
          <div
            ref={backdrop}
            className={cx(
              classNames.backdrop,
              active && open && classNames.backdrop__active,
              noBackdrop && classNames.backdrop__disabled,
            )}
          >
            <div className={classNames.container} style={{ width }}>
              {children}
              {showCloseButton && (
                <Button
                  icon={'Close'}
                  size="tiny"
                  rounded
                  fitContent
                  onClick={() => onClose?.()}
                  className={classNames.close}
                />
              )}
            </div>
          </div>
        </Portal>
      )}
    </Fragment>
  );
};
