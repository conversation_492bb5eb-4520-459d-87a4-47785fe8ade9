.wrapper {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 7;
}

.wrapperNoBackdrop {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}

.backdrop {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  background-color: rgba(51, 51, 51, 0.3);
  pointer-events: all;
  backdrop-filter: blur(1px);
  opacity: 0;
  transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.backdrop__disabled {
  background-color: transparent;
  pointer-events: none;
}

.backdrop__active {
  opacity: 1;
}

.backdrop__off {
  position: relative;
}

.container {
  position: relative;
  box-sizing: border-box;
  min-height: 50px;
  min-width: 50px;
  max-height: calc(100vh - 10px);
  max-width: calc(100vw - 80px);
  box-shadow: 0 6px 12px 0 #00000033;
  background-color: white;
  border-radius: 12px;
  pointer-events: all;
  padding: 24px;
  display: flex;
  flex-direction: column;
  transform: translateY(50px);
  opacity: 0;
  transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.backdrop__active .container {
  transform: translateY(0);
  opacity: 1;
}

/* Sizes */
.size__small {
  width: 400px;
}
.size__small_xl {
  width: 440px;
}
.size__default {
  width: 520px;
}
.size__medium {
  width: 760px;
}
.size__large {
  width: 960px;
}

.modalSection {
  padding: 24px 0 0;
}

.modalSectionDisabled {
  opacity: 50%;
}

.text {
  margin-top: 8px;
}

.buttons {
  margin-top: 24px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 8px;
}

.buttonsSpaceBetween {
  justify-content: space-between;
}

.close {
  position: absolute;
  right: 15px;
  top: 16px;
  border-radius: 8px;
  padding: 4px;
  transition: background-color 0.3s;
}

.close:hover {
  background-color: var(--color-grey-10);
}

.divider {
  border-bottom: 1px solid var(--color-grey-30);
  margin: 24px -24px;
}
