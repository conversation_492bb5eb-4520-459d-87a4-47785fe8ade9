import { ReactNode } from 'react';

export enum ModalSize {
  Small = 'small', // 400 confirmation modals
  SmallXl = 'small_xl', // 440
  Default = 'default', // 520 default modals
  Medium = 'medium', // 760 bigger than default
  Large = 'large', // 960 big modals with map
}

export interface ModalProps {
  /**
   * @deprecated Use size instead
   */
  width?: number;
  size?: ModalSize;
  height?: number;
  open: boolean;
  locked?: boolean;
  children: ReactNode;
  showCloseButton?: boolean;
  noBackdrop?: boolean;
  className?: string;
  wrapperClassname?: string;
  onShow?: Function;
  onClose?: Function;
  onSubmit?: Function;
  mode?: 'relative' | 'absolute';
}

export interface ModalDeprecatedProps {
  width: number;
  open: boolean;
  locked?: boolean;
  children: ReactNode;
  showCloseButton?: boolean;
  noBackdrop?: boolean;
  onClose?: Function;
}
