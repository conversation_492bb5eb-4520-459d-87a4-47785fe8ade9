import React, { FC, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useStoreMap } from 'effector-react';
import cx from 'classnames';

import { MenuHeader } from 'components/MenuHeader';
import MenuLink from 'components/MenuLink';
import { usePopover } from 'components/Popover/Popover';
import SimpleLink from 'components/SimpleLink';
import SvgIcon from 'components/SvgIcon';
import { Text } from 'components/Text';
import { Tooltip } from 'components/Tooltip/Tooltip';
import AttributeEditFlow from 'features/field-page/FieldData/AttributeEditModal/AttributeEditFlow';
import FeatureDisabledTooltip from '../../features/demo-account/FeatureDisabledTooltip';
import LayerMenuLink from './LayerMenuLink';
import { Hint } from '../Tooltip/Hint';

import { toggleAttributeModal } from 'features/field-page/FieldData/AttributeEditModal/models/attribute-edit-form';
import { fieldSeasonReports$ } from 'features/field-page/models/report';
import {
  BaseLayers,
  FieldMapLayer,
  FieldSeason,
  FillingLayers,
  isFieldBaseLayer,
  isPlanetSupportedLayer,
  LayersWithDescription,
} from 'models/fieldSeasons';
import { isTypeEnabled } from 'utils/map-filling';
import { ImportType } from 'features/import-files/models/types';
import {
  getSoilSamplingAttributesFromResults,
  getSoilSamplingResultGroupsByRawData,
  useSoilSamplingResults,
} from 'features/soil-sampling-results/utils';
import { useMachineryInfo } from 'features/machinery/utils';
import {
  getElectroconductivityAttributesFromResults,
  getElectroconductivityGroups,
  useElectroconductivityResults,
} from 'features/electroconductivity/utils';

import classNames from './MapFieldSidebar.module.css';

type Props = {
  fieldSeason: FieldSeason;
  selectedLayer: FieldMapLayer;
  isPlanet: boolean;
  setSelectedLayer: (vals: {
    layer: FieldMapLayer;
    index: number;
    attributeType: ImportType | null;
    isPlanet: boolean;
  }) => void;
  attributeType: ImportType | null;
};

const EditMenu = () => {
  const { hide } = usePopover();
  const { t } = useTranslation();

  return (
    <FeatureDisabledTooltip restrictedRoles={['viewer']}>
      <MenuLink
        onClick={() => {
          toggleAttributeModal();
          hide();
        }}
        className={classNames.menuLink}
      >
        <Text size={14} className={classNames.menuLinkText}>
          <SvgIcon name="FieldData" width={16} height={16} />
          {t('soil-sampling.filling-sidebar.edit-data')}
        </Text>
      </MenuLink>
    </FeatureDisabledTooltip>
  );
};

const MapFieldSidebar: FC<Props> = ({
  fieldSeason,
  selectedLayer,
  isPlanet,
  setSelectedLayer,
  attributeType,
}) => {
  const { t } = useTranslation();
  const report = useStoreMap(
    fieldSeasonReports$,
    (items) => items[fieldSeason.active_pa_info_uuid!],
  );

  const soilSamplingResults = useSoilSamplingResults(fieldSeason.uuid);
  const electroConductivityResults = useElectroconductivityResults(
    fieldSeason.uuid,
  );

  const soilSamplingResultAttributes =
    getSoilSamplingAttributesFromResults(soilSamplingResults);

  const handleLayerSelect = useCallback(
    (type) => {
      const isEnabled = isTypeEnabled(type, report);
      if (isEnabled) {
        setSelectedLayer({
          layer: type,
          index: 0,
          isPlanet,
          attributeType: null,
        });
      }
    },
    [isPlanet, report, setSelectedLayer],
  );

  const { machineryGroups } = useMachineryInfo(fieldSeason);
  const soilSamplingGroups = useMemo(
    () => getSoilSamplingResultGroupsByRawData(soilSamplingResults),
    [soilSamplingResults],
  );
  const electroConductivityGroups = useMemo(
    () => getElectroconductivityGroups(electroConductivityResults),
    [electroConductivityResults],
  );
  const electroConductivityAttributes =
    getElectroconductivityAttributesFromResults(electroConductivityResults);

  // handle removing of soil analysis attribute
  useEffect(() => {
    const groupLayers = soilSamplingGroups.map(({ id }) => id);
    const machineGroups = machineryGroups.machineryNames.map(({ id }) => id);
    const electroGroups = electroConductivityGroups.map(({ id }) => id);

    const layers = {
      [ImportType.SoilSampling]: groupLayers,
      [ImportType.Harvesting]: machineGroups,
      [ImportType.ElectroConductivity]: electroGroups,
    };

    if (
      (attributeType === ImportType.SoilSampling &&
        !groupLayers.includes(selectedLayer as string)) ||
      (attributeType === ImportType.ElectroConductivity &&
        !electroGroups.includes(selectedLayer as string)) ||
      (attributeType === ImportType.Harvesting &&
        !machineGroups.includes(selectedLayer as string))
    ) {
      setSelectedLayer({
        layer:
          layers?.[attributeType as ImportType]?.[0] ||
          BaseLayers.filter((layer) => {
            return isTypeEnabled(layer, report);
          })?.[0] ||
          FillingLayers.ProductivityMap,
        index: 0,
        attributeType: isFieldBaseLayer(selectedLayer) ? null : attributeType,
        isPlanet: false,
      });
    }
  }, [
    soilSamplingGroups,
    selectedLayer,
    setSelectedLayer,
    machineryGroups,
    electroConductivityGroups,
    attributeType,
    report,
  ]);

  const isElectroConductivityLayer =
    attributeType === ImportType.ElectroConductivity;

  const resultAttributes = isElectroConductivityLayer
    ? electroConductivityAttributes
    : soilSamplingResultAttributes;

  return (
    <>
      <div className={classNames.sidebar}>
        <ul className={classNames.menu}>
          {BaseLayers.map((type) => {
            const isLayerAvailable =
              (isPlanet &&
                (isPlanetSupportedLayer(type) ||
                  type === FillingLayers.NoMap)) ||
              !isPlanet;
            const isEnabled = isLayerAvailable && isTypeEnabled(type, report);
            const isWithDescription = LayersWithDescription.includes(type);

            return (
              <li key={type}>
                <Tooltip
                  placement="right"
                  arrow
                  disabled={isEnabled}
                  content={t('zone-editor.base-layer.disabled')}
                >
                  <MenuLink
                    as={SimpleLink}
                    theme="dark"
                    active={selectedLayer === type}
                    className={cx(
                      classNames.link,
                      !isEnabled && classNames.disabledLayer,
                    )}
                    onClick={() => {
                      if (!isEnabled) {
                        return;
                      }
                      handleLayerSelect(type);
                    }}
                  >
                    {t(`zone-editor.base-layer.${type || 'none'}`)}
                    {isWithDescription && (
                      <Hint content={t(`map.filling.${type}.description`)}>
                        <SvgIcon
                          name="Info"
                          width={16}
                          height={16}
                          color="var(--color-black-light)"
                        />
                      </Hint>
                    )}
                  </MenuLink>
                </Tooltip>
              </li>
            );
          })}
          {machineryGroups.machineryNames.length > 0 && (
            <MenuHeader
              title={t('soil-sampling.filling-sidebar.groups.harvesting')}
            />
          )}
          {machineryGroups.machineryNames.map(({ id, label }) => {
            const formattedLabel = label?.toString() || '';
            const isActive =
              selectedLayer === id && attributeType === ImportType.Harvesting;

            return (
              <LayerMenuLink
                id={id}
                key={id}
                attributeType={ImportType.Harvesting}
                label={formattedLabel}
                isActive={isActive}
                onClick={() => {
                  setSelectedLayer({
                    layer: id,
                    index: 0,
                    attributeType: ImportType.Harvesting,
                    isPlanet: false,
                  });
                }}
              />
            );
          })}
          {soilSamplingGroups.length > 0 && (
            <MenuHeader
              title={t('soil-sampling.filling-sidebar.groups.soil-analysis')}
            />
          )}
          {soilSamplingGroups.map(({ id, label, isCustom }) => {
            const formattedLabel = label?.toString() || '';

            const isActive =
              selectedLayer === id && attributeType === ImportType.SoilSampling;

            return (
              <LayerMenuLink
                id={id}
                key={id}
                attributeType={ImportType.SoilSampling}
                label={formattedLabel}
                isActive={isActive}
                isCustom={isCustom}
                onClick={() => {
                  setSelectedLayer({
                    layer: id,
                    index: 0,
                    attributeType: ImportType.SoilSampling,
                    isPlanet: false,
                  });
                }}
              >
                <EditMenu />
              </LayerMenuLink>
            );
          })}
          {electroConductivityGroups.length > 0 && (
            <MenuHeader
              title={t('electroconductivity.filling-sidebar.groups.title')}
            />
          )}
          {electroConductivityGroups.map(({ id, label }) => {
            const formattedLabel = label?.toString() || '';
            const attr = electroConductivityAttributes.find(
              (attr) => attr.targetName === label,
            );

            const isActive =
              selectedLayer === id &&
              attributeType === ImportType.ElectroConductivity;

            return (
              <LayerMenuLink
                id={id}
                key={id}
                attributeType={ImportType.ElectroConductivity}
                label={formattedLabel}
                isCustom={attr?.isCustom}
                isActive={isActive}
                onClick={() => {
                  setSelectedLayer({
                    layer: id,
                    index: 0,
                    attributeType: ImportType.ElectroConductivity,
                    isPlanet: false,
                  });
                }}
              >
                <EditMenu />
              </LayerMenuLink>
            );
          })}
        </ul>
      </div>
      <AttributeEditFlow
        resultAttributes={resultAttributes}
        isElectroConductivityLayer={
          attributeType === ImportType.ElectroConductivity
        }
        targetName={selectedLayer}
        fieldSeason={fieldSeason}
      />
    </>
  );
};

export default MapFieldSidebar;
