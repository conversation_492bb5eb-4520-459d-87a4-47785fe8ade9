.fullscreen {
  display: flex;
  flex-direction: column;
  padding: 8px;
  max-width: 100vw;
  max-height: 100vh;
  width: 100%;
  height: 100%;
  color: var(--color-white);
  border-radius: unset;
  background-color: var(--color-black);
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 8px;
  flex-direction: column;
}

.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  margin: 0 8px;
  font-weight: 500;
}

.fieldInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fieldInfoArea {
  color: var(--color-black-light)
}

.mapContainer {
  position: relative;
  flex: 1;
  display: flex;
  overflow: hidden;
  gap: 8px;
  flex-wrap: wrap;
}

.cell {
  position: relative;
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
}

.row {
  display: flex;
  gap: 8px;
}

.top {
  z-index: 6;
}

.measurePanel {
  z-index: 2;
}

.planetProcessing {
  width: 392px;
  height: 142px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--color-black);
  border-radius: 12px;
  gap: 12px;
}

.planetProcessingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-white);
}

.planetProcessingContent > div:nth-child(2) {
  font-size: 12px;
  font-weight: 400;
  color: var(--color-grey-70);
}
