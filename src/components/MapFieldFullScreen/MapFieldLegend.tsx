import React from 'react';

import { ElectroConductivityDefaultColors } from 'constants/palette';
import {
  FieldSeason,
  FillingLayers,
  FillingLegend,
  isVRAFillingLayer,
} from 'models/fieldSeasons';
import { TiffData } from 'features/raster-layer/models/types';
import { ImportType } from 'features/import-files/models/types';

import MapLegendPopover from 'components/MapLegendPopover/MapLegendPopover';
import SoilSamplingResultAttributeLegend from 'features/field-page/FieldData/SoilSamplingResultAttributeCard/SoilSamplingResultAttributeLegend';
import RasterLegend from 'features/raster-layer/RasterLegend/RasterLegend';
import VRAEditorOverlay from 'features/vra/VRAEditorOverlay/VRAEditorOverlay';
import { layerToSource } from 'utils/vra';
import { useSoilSamplingResultAttribute } from '../../features/soil-sampling-results/utils';

type MapFieldLegendProps = {
  selectedLayer: string;
  fieldSeason: FieldSeason;
  resultUuid: string;
  palette: Record<string, string> | null;
  type: string | null;
  legend?: FillingLegend | null;
  zoneCount?: number;
  isNDVILayer: boolean;
  tiffData?: TiffData | null;
};

const MapFieldLegend = ({
  selectedLayer,
  fieldSeason,
  type,
  resultUuid,
  palette,
  legend,
  zoneCount,
  isNDVILayer,
  tiffData,
}: MapFieldLegendProps) => {
  const attribute = useSoilSamplingResultAttribute(
    fieldSeason.uuid,
    selectedLayer,
    resultUuid,
  );
  return (
    <MapLegendPopover isVisibleByDefault>
      {isVRAFillingLayer(selectedLayer) && zoneCount && (
        <VRAEditorOverlay
          fieldSeason={fieldSeason}
          vraSource={layerToSource(selectedLayer)}
          zoneCount={zoneCount}
        />
      )}
      {type === ImportType.SoilSampling &&
        attribute &&
        !isVRAFillingLayer(selectedLayer) && (
          <SoilSamplingResultAttributeLegend attribute={attribute} />
        )}
      {type === ImportType.Harvesting && palette && (
        <RasterLegend
          filling={selectedLayer as FillingLayers}
          palette={palette}
          min={legend?.min}
          max={legend?.max}
          unit={legend?.unit}
        />
      )}
      {type === ImportType.ElectroConductivity && (
        <RasterLegend
          filling={FillingLayers.ContrastedNdvi}
          palette={ElectroConductivityDefaultColors}
          min={legend?.min}
          max={legend?.max}
        />
      )}
      {!type && tiffData && (
        <RasterLegend
          filling={selectedLayer as FillingLayers}
          withClouds={isNDVILayer}
          min={tiffData?.min}
          max={tiffData?.max}
        />
      )}
    </MapLegendPopover>
  );
};

export default MapFieldLegend;
