import React, { FC, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useStore, useStoreMap } from 'effector-react';
import { useTranslation } from 'react-i18next';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import { FeatureCollection, LineString, Polygon } from '@turf/helpers';

import LayerPlaceholder from 'components/LayerPlaceholder';
import { MapBase, MapBaseProps } from 'components/MapBase';
import MenuSwitcher from 'components/MenuSwitcher/MenuSwitcher';
import PositionTo from 'components/PositionTo/PositionTo';
import TerrainLayer from 'components/TerrainLayer/TerrainLayer';
import VRAZonesLayer from 'features/vra/VRAZonesLayer';
import VRAStripesLayer from 'features/vra/VRAStripesLayer';
import VRAControlLinesLayer from 'features/vra/VRAControlLinesLayer';
import { Text } from 'components/Text';
import { ndviMetaByField$ } from '../../features/vra/models/ndvi';
import { useFieldSeasonCalendarSelectedDate } from '../../models/availableDates/utils';
import MapFieldLegend from './MapFieldLegend';
import MapFieldLayers from './MapFieldLayers';
import { PlanetSwitcher } from '../PlanetSwitcher/PlanetSwitcher';
import SoilSamplingChangeMapModal from 'features/soil-sampling-results/SoilSamplingChangeMapModal/SoilSamplingChangeMapModal';
import Loader from '../Loader/Loader';

import BaseFieldLayer from 'features/editor-common/BaseFieldLayer';
import HarvestingAttributesButton from 'features/machinery/HarvestingAttributesButton/HarvestingAttributesButton';
import FillTypeButton from 'features/editor-common/FillTypeButton/FillTypeButton';
import {
  getAvailableDateImageByIdOrDate,
  getCurrentLegend,
  useRasterLayerUrl,
  useTiffData,
} from 'features/raster-layer/RasterLegend/utils';
import {
  getResultNameByAttribute,
  useSoilSamplingResultAttribute,
  useSoilSamplinResultsData,
} from 'features/soil-sampling-results/utils';
import {
  useElectroconductivityAttribute,
  useElectroconductivityData,
} from 'features/electroconductivity/utils';
import FieldBorderLayer from 'features/soil-sampling/SoilSamplingLayers/FieldBorderLayer';
import DateControl from 'features/vra/DateControl';
import {
  FieldBaseLayer,
  FieldMapLayer,
  FieldSeason,
  FillingLayers,
  isPlanetSupportedLayer,
  isRasterDateLayer,
} from 'models/fieldSeasons';
import { LayerMode, mouseMoved, ViewState } from 'models/map';
import {
  linesPreview$,
  vraMapControlLines$,
  zones$,
} from 'features/vra/models/editor';
import { getMapBound } from 'utils/map';
import {
  hasAttributeInOperation,
  useHarvestPalletteByAttribute,
} from 'features/machinery/utils';
import useOperationsData from 'features/machinery/use-operations-data';
import { initMapMeasureDraw } from 'features/map-measure/MapMeasureRulerMode';
import { toggleMapDrawMode } from 'features/map-measure/utils';
import { changeSoilSamplingMapModal$ } from 'features/soil-sampling-results/models/soil-sampling-results';
import { ImportType } from 'features/import-files/models/types';
import { vraMapsByUuid$ } from 'models/vraMaps';
import { TiffData } from 'features/raster-layer/models/types';
import { workspaceAccess$ } from 'features/multiaccount/models/multiaccount';
import { getLayerTitle } from 'features/field-page/FieldData/utils';
import { isPlanetMetaProcessingByFieldUserSeasonUuid$ } from '../../models/planet';

import classNames from './MapFieldFullScreen.module.css';

type Props = {
  fieldSeason: FieldSeason;
  selectedLayer: FieldMapLayer;
  isPlanet: boolean;
  compareLayer?: FieldMapLayer;
  defaultResultUuid?: string | null;
  defaultOperationUuid?: string | null;
  defaultHarvestAttr?: string | null;
  setSelectedLayer: ({
    layer,
    index,
    attributeType,
    isPlanet,
  }: {
    layer: FieldMapLayer;
    index: number;
    attributeType: ImportType | null;
    isPlanet: boolean;
  }) => void;
  attributeType?: ImportType | null;
  layerMode: LayerMode;
  zoneCount?: number;
  ndviTiffData?: TiffData | null;
  bbox: [number, number, number, number];
  viewState: ViewState | null;
  onSetViewState: (partialViewState: Partial<ViewState>) => void;
  instanceId: string;
  isSplitView: boolean;
  rulerEnabled: boolean;
  drawState: { features: FeatureCollection; mode: string } | null;
  onDrawStateChange: (payload: {
    features: FeatureCollection;
    mode: string;
  }) => void;
};

const bboxPadding = 64;

const MapFieldInstance: FC<Props> = ({
  bbox,
  viewState,
  onSetViewState,
  fieldSeason,
  selectedLayer,
  compareLayer,
  defaultResultUuid,
  defaultOperationUuid,
  defaultHarvestAttr,
  attributeType,
  setSelectedLayer,
  layerMode,
  zoneCount,
  instanceId,
  isSplitView,
  rulerEnabled,
  drawState,
  onDrawStateChange,
  ndviTiffData,
  isPlanet,
}) => {
  const { t, i18n } = useTranslation();
  const [isMapLoaded, setIsMapLoaded] = useState<boolean>(false);
  const [mapDraw, setMapDraw] = useState<MapboxDraw | null>(null);
  const { has_planet_access } = useStore(workspaceAccess$);
  const withPlanetAvailability =
    isPlanetSupportedLayer(selectedLayer as FieldBaseLayer) &&
    has_planet_access &&
    fieldSeason.is_activated;

  const isPlanetMetaProcessing =
    useStoreMap({
      store: isPlanetMetaProcessingByFieldUserSeasonUuid$,
      keys: [fieldSeason.uuid],
      fn: (isProcessing) => isProcessing[fieldSeason.uuid] ?? false,
    }) && isPlanet;

  useEffect(() => {
    if (!mapDraw) return;
    // set draw mode for all instances of map
    toggleMapDrawMode({ mapDraw, enabled: rulerEnabled });
  }, [rulerEnabled, mapDraw]);

  useEffect(() => {
    if (!mapDraw) return;
    if (drawState) {
      // if draw mode of one of instances is not equal to current mode, change it to simple_select
      if (mapDraw.getMode() !== drawState.mode) {
        mapDraw.changeMode('simple_select');
      }
      // set draw state for all instances of map
      mapDraw.set(
        drawState.features as FeatureCollection<Polygon | LineString>,
      );
    }
  }, [mapDraw, drawState]);

  const changeSoilSamplingMapModal = useStore(changeSoilSamplingMapModal$);

  // Operations
  const [selectedHarvestAttribute, setSelectedHarvestAttribute] = useState<
    string | null
  >(defaultHarvestAttr || null);
  const { palette } = useHarvestPalletteByAttribute(selectedHarvestAttribute);
  const {
    machineryGroups,
    selectedOperationUuid,
    setSelectedOperationUuid,
    machineryOperationOptions,
  } = useOperationsData({
    fieldUuid: fieldSeason.uuid,
    selectedLayer,
    defaultOperationUuid,
    i18n,
  });

  const operation = machineryGroups.machineryAttributes.find(
    (a) => a.machineryOperationUuid === selectedOperationUuid,
  );

  useEffect(() => {
    if (
      operation &&
      !hasAttributeInOperation(operation, selectedHarvestAttribute)
    ) {
      setSelectedHarvestAttribute(operation?.defaultAttribute!);
    }
  }, [setSelectedHarvestAttribute, selectedHarvestAttribute, operation]);

  // VRA
  const zones = useStore(zones$);
  const linesPreview = useStore(linesPreview$);
  const vraMapControlLines = useStore(vraMapControlLines$);
  const controlLines = linesPreview || vraMapControlLines;
  const { vraMapUuid } = useParams<{
    vraMapUuid: string;
  }>();
  const vraMapList = useStore(vraMapsByUuid$);
  const vraMap = vraMapList[vraMapUuid || ''];

  // Soil sampling
  const { selectedResultUuid, setSelectedResultUuid, soilSamplingOptions } =
    useSoilSamplinResultsData({
      layer: selectedLayer,
      fieldUuid: fieldSeason.uuid,
      i18n,
      defaultUuid: defaultResultUuid || null,
    });
  const ssAttribute = useSoilSamplingResultAttribute(
    fieldSeason.uuid,
    selectedLayer,
    selectedResultUuid,
  );

  // Electroconductivity
  const { selectedEcUuid, setSelectedEcUuid, electroconductivityOptions } =
    useElectroconductivityData({
      layer: selectedLayer,
      fieldUuid: fieldSeason.uuid,
      i18n,
      defaultUuid: defaultResultUuid || null,
    });
  const ecAttribute = useElectroconductivityAttribute(
    fieldSeason,
    selectedLayer,
    selectedEcUuid,
  );

  const ndviMeta = useStoreMap(
    ndviMetaByField$,
    (items) => items[fieldSeason.uuid] || [],
  );

  const isNDVILayer =
    selectedLayer === FillingLayers.Ndvi ||
    selectedLayer === FillingLayers.ContrastedNdvi;
  const isRGBLayer = selectedLayer === FillingLayers.SatelliteImage;

  const isLayerWithDates = isRasterDateLayer(selectedLayer as FieldBaseLayer);
  const isVegetationLayer = selectedLayer === 'vegetation';

  const isNDVICompareLayer =
    compareLayer === FillingLayers.Ndvi ||
    compareLayer === FillingLayers.ContrastedNdvi;

  let calendarId = `map-field-instance-${instanceId}`;
  if (
    instanceId === '0' &&
    isSplitView &&
    selectedLayer === 'vegetation' &&
    isNDVICompareLayer
  ) {
    calendarId = 'map-field-instance-1';
  }
  let selectedDate = useFieldSeasonCalendarSelectedDate({
    calendarId,
    fieldSeasonUuid: fieldSeason.uuid,
    isPlanet,
  });
  if (!isNDVICompareLayer && !isLayerWithDates) {
    selectedDate = undefined;
  }

  const selectedNdviMeta = ndviMeta.find(
    (meta) => meta.date === selectedDate?.date,
  );
  let url: string | undefined;

  const attribute = useMemo(() => {
    if (attributeType === ImportType.SoilSampling) {
      return ssAttribute;
    }
    if (attributeType === ImportType.ElectroConductivity) {
      return ecAttribute;
    }
    return null;
  }, [ssAttribute, ecAttribute, attributeType]);
  const currentLegend = getCurrentLegend({
    attributeType,
    selectedHarvestAttribute,
    operation,
    ecAttribute,
  });

  const availableDateUrl = useRasterLayerUrl({
    fieldSeason,
    layer: isVegetationLayer ? 'ndvi' : selectedLayer,
    date: selectedDate?.date,
    isPlanet,
  });
  if (selectedNdviMeta && isNDVILayer) {
    url = getAvailableDateImageByIdOrDate(
      fieldSeason.uuid,
      selectedNdviMeta.date,
      isNDVILayer ? FillingLayers.Ndvi : selectedLayer,
      isPlanet,
      fieldSeason.geom_updated_at,
    );
  } else {
    url = availableDateUrl;
  }
  let tiffData = useTiffData(url);
  if (!tiffData && isVegetationLayer) {
    tiffData = ndviTiffData || null;
  }

  const initialViewState: MapBaseProps['initialViewState'] = {
    bounds: bbox,
    fitBoundsOptions: {
      padding: bboxPadding,
    },
  };

  const updateDrawMeasures = (draw: MapboxDraw) => {
    const features = draw.getAll() as FeatureCollection;
    const mode = draw.getMode();
    onDrawStateChange({ features, mode });
  };

  return (
    <>
      <MapBase
        id={`map-field-instance-${instanceId}`}
        initialViewState={initialViewState}
        preserveDrawingBuffer
        onLoad={({ target }) => {
          setIsMapLoaded(true);
          onSetViewState({
            zoom: target.getZoom(),
            latitude: target.getCenter().lat,
            longitude: target.getCenter().lng,
          });
          const draw = initMapMeasureDraw(target, {
            onAddPoint: (_, draw) => updateDrawMeasures(draw),
            onDrawPolygon: (_, draw) => updateDrawMeasures(draw),
            onDrawUpdate: (_, draw) => updateDrawMeasures(draw),
            onCreateLine: (_, draw) => updateDrawMeasures(draw),
          });
          setMapDraw(draw);
        }}
        onMove={({ viewState }) => {
          if (!isMapLoaded) return;
          onSetViewState({
            latitude: viewState.latitude,
            longitude: viewState.longitude,
            zoom: viewState.zoom,
          });
        }}
        onMouseMove={mouseMoved}
        onMapResize={(viewState) => {
          const bound = getMapBound(
            viewState.width,
            viewState.height,
            bbox,
            bboxPadding,
          );
          onSetViewState({
            ...bound,
            width: viewState.width,
            height: viewState.height,
          });
        }}
        {...viewState}
      >
        <LayerPlaceholder id="layer-placeholder" />
        <TerrainLayer isMapLoaded={isMapLoaded} layerMode={layerMode} />
        <FieldBorderLayer
          fieldSeason={fieldSeason}
          layerId="field-border"
          beforeId="layer-placeholder"
        />
        <BaseFieldLayer
          fieldSeason={fieldSeason}
          layer={selectedLayer as FieldBaseLayer}
          isPlanet={isPlanet}
          availableDate={selectedDate}
          withoutHover={rulerEnabled}
          beforeId="field-border"
        />
        <MapFieldLayers
          {...{
            selectedLayer,
            fieldSeason,
            rulerEnabled,
            operation,
            attributeType,
            selectedResultUuid,
            selectedEcUuid,
            selectedHarvestAttribute,
          }}
        />
        {zoneCount && (
          <VRAZonesLayer
            fieldSeason={fieldSeason}
            zoneCount={zoneCount}
            filling={selectedLayer as FieldMapLayer}
            zones={zones}
            ndviTiffData={tiffData}
            vraMap={vraMap}
            beforeId="layer-placeholder"
          />
        )}
        {controlLines && zoneCount && (
          <VRAControlLinesLayer
            fieldSeason={fieldSeason}
            filling={selectedLayer as FieldMapLayer}
            zoneCount={zoneCount}
            controlLines={controlLines}
            ndviTiffData={tiffData}
            vraMap={vraMap}
            beforeId="layer-placeholder"
          />
        )}
        <VRAStripesLayer
          fieldSeason={fieldSeason}
          beforeId="layer-placeholder"
        />
      </MapBase>

      {isSplitView && (
        <PositionTo
          className={classNames.top}
          placement="left top"
          offsetX={12}
          offsetY={12}
        >
          <div className={classNames.row}>
            <FillTypeButton
              fieldSeason={fieldSeason}
              layer={selectedLayer as FieldBaseLayer}
              attributeType={attributeType}
              isPlanet={isPlanet}
              isCustom={attribute?.isCustom}
              onLayerChange={({ layer, attributeType, isPlanet }) => {
                setSelectedLayer({
                  layer: layer,
                  index: Number(instanceId),
                  attributeType,
                  isPlanet,
                });
              }}
            />
            {withPlanetAvailability && (
              <PlanetSwitcher
                mode="dark"
                value={isPlanet}
                onChange={(isPlanet) => {
                  setSelectedLayer({
                    layer: selectedLayer,
                    index: Number(instanceId),
                    attributeType: attributeType || null,
                    isPlanet,
                  });
                }}
              />
            )}
            <DateControl
              fieldSeason={fieldSeason}
              calendarId={calendarId}
              filling={selectedLayer as FieldBaseLayer}
              isVisible={isLayerWithDates}
              isPlanet={isPlanet}
            />
            {attributeType === ImportType.SoilSampling && (
              <MenuSwitcher
                value={selectedResultUuid}
                onClick={setSelectedResultUuid}
                isDropdownView
                options={soilSamplingOptions}
              />
            )}
            {attributeType === ImportType.Harvesting && (
              <>
                {operation && (
                  <HarvestingAttributesButton
                    selectedAttribute={selectedHarvestAttribute}
                    setSelectedAttribute={setSelectedHarvestAttribute}
                    operation={operation}
                    fieldSeason={fieldSeason}
                  />
                )}
                <MenuSwitcher
                  value={selectedOperationUuid}
                  onClick={setSelectedOperationUuid}
                  isDropdownView
                  options={machineryOperationOptions}
                />
              </>
            )}
            {attributeType === ImportType.ElectroConductivity && (
              <MenuSwitcher
                value={selectedEcUuid}
                onClick={setSelectedEcUuid}
                isDropdownView
                options={electroconductivityOptions}
              />
            )}
          </div>
        </PositionTo>
      )}

      {!isSplitView && (
        <>
          <PositionTo placement="left top" offsetX={16} offsetY={15}>
            {selectedLayer && !operation && (
              <div className={classNames.row}>
                <div>
                  {!withPlanetAvailability && (
                    <Text size={18} weight="medium">
                      {getLayerTitle(
                        i18n,
                        selectedLayer,
                        attributeType,
                        attribute?.isCustom,
                      )}
                    </Text>
                  )}
                  {withPlanetAvailability && (
                    <PlanetSwitcher
                      mode="dark"
                      value={isPlanet}
                      onChange={(isPlanet) => {
                        setSelectedLayer({
                          layer: selectedLayer,
                          index: Number(instanceId),
                          attributeType: attributeType || null,
                          isPlanet,
                        });
                      }}
                    />
                  )}
                </div>
              </div>
            )}
            <div>
              {soilSamplingOptions.length === 1 && (
                <Text size={12}>
                  {getResultNameByAttribute(
                    i18n,
                    attribute?.resultName || '',
                    attribute?.date,
                  )}
                </Text>
              )}
              {operation && (
                <HarvestingAttributesButton
                  selectedAttribute={selectedHarvestAttribute}
                  setSelectedAttribute={setSelectedHarvestAttribute}
                  operation={operation}
                  fieldSeason={fieldSeason}
                />
              )}
            </div>
          </PositionTo>
          <PositionTo placement={'center top'} offsetY={15}>
            <DateControl
              fieldSeason={fieldSeason}
              calendarId={calendarId}
              filling={selectedLayer as FieldBaseLayer}
              isVisible={isLayerWithDates}
              isPlanet={isPlanet}
            />
          </PositionTo>
          <PositionTo placement={'center top'} offsetY={15}>
            {attributeType === ImportType.SoilSampling &&
              soilSamplingOptions.length > 1 && (
                <MenuSwitcher
                  value={selectedResultUuid}
                  onClick={setSelectedResultUuid}
                  options={soilSamplingOptions}
                />
              )}
            {attributeType === ImportType.Harvesting &&
              machineryOperationOptions.length > 1 && (
                <MenuSwitcher
                  value={selectedOperationUuid}
                  onClick={setSelectedOperationUuid}
                  options={machineryOperationOptions}
                />
              )}

            {attributeType === ImportType.ElectroConductivity &&
              electroconductivityOptions.length > 1 && (
                <MenuSwitcher
                  value={selectedEcUuid}
                  onClick={setSelectedEcUuid}
                  options={electroconductivityOptions}
                />
              )}
          </PositionTo>
        </>
      )}

      {!!changeSoilSamplingMapModal && (
        <SoilSamplingChangeMapModal
          changeSoilSamplingMapModal={changeSoilSamplingMapModal}
        />
      )}

      {isPlanetMetaProcessing && (
        <div className={classNames.planetProcessing}>
          <Loader loading inline size={24} color="var(--color-white)" />
          <div className={classNames.planetProcessingContent}>
            <div>{t('field-status.info.planet.processing.title')}</div>
            <div>{t('field-status.info.planet.processing.description')}</div>
          </div>
        </div>
      )}

      {selectedLayer && selectedLayer !== FillingLayers.NoMap && !isRGBLayer && (
        <PositionTo placement="left bottom" offsetX={8} offsetY={8}>
          <MapFieldLegend
            selectedLayer={selectedLayer}
            type={attributeType!}
            tiffData={tiffData}
            palette={palette}
            zoneCount={zoneCount}
            legend={currentLegend}
            fieldSeason={fieldSeason}
            resultUuid={selectedResultUuid}
            isNDVILayer={isNDVILayer}
          />
        </PositionTo>
      )}
    </>
  );
};

export default MapFieldInstance;
