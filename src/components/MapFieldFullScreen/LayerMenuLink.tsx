import React from 'react';
import { useTranslation } from 'react-i18next';

import MenuLink from 'components/MenuLink';
import Popover, { NavBox } from 'components/Popover/Popover';
import IconButton from 'components/IconButton/IconButton';
import SimpleLink from 'components/SimpleLink';

import { getLayerTitle } from 'features/field-page/FieldData/utils';

import { ImportType } from 'features/import-files/models/types';

import classNames from './MapFieldSidebar.module.css';

type Props = {
  id: string;
  label: string;
  attributeType?: ImportType;
  isActive: boolean;
  isCustom?: boolean;
  onClick: () => void;
  children?: React.ReactNode;
};

const LayerMenuLink = ({
  id,
  label,
  attributeType,
  isCustom,
  isActive,
  onClick,
  children,
}: Props) => {
  const { i18n } = useTranslation();

  return (
    <MenuLink
      className={classNames.link}
      onClick={onClick}
      active={isActive}
      as={SimpleLink}
      theme="dark"
    >
      {getLayerTitle(i18n, label, attributeType, isCustom)}
      {isActive && (
        <Popover
          trigger="click"
          interactive
          offset={[60, 10]}
          render={(attrs) => <NavBox {...attrs}>{children}</NavBox>}
          placement="bottom"
        >
          <IconButton
            className={classNames.iconButton}
            iconSize={16}
            icon="More"
          />
        </Popover>
      )}
    </MenuLink>
  );
};

export default LayerMenuLink;
