import React, { useState } from 'react';
import { Popup } from 'react-map-gl';
import { useTranslation } from 'react-i18next';
import { Geometry } from '@turf/helpers';

import InteractiveVectorTooltip from 'components/MapFieldFullScreen/InteractiveVectorTooltip';

import useLayerEvents from 'utils/use-layer-events';
import { ImportType } from 'features/import-files/models/types';
import { SoilSamplingResultAttribute } from '../../features/soil-sampling-results/models/types';
import { ElectroConductivityResultAttribute } from 'features/electroconductivity/models/types';

import classNames from 'components/MapFieldFullScreen/HoverTooltip.module.css';

type Props = {
  layerId: string;
  attribute: SoilSamplingResultAttribute | ElectroConductivityResultAttribute;
  fieldsSeasonGeom?: Geometry;
};

const ResultVectorHoverLayer = ({
  attribute,
  layerId,
  fieldsSeasonGeom,
}: Props) => {
  const { t } = useTranslation();
  const [hoveredInfo, setHoveredInfo] = useState<{
    id: string | number;
    value: string;
    lng: number;
    lat: number;
  } | null>(null);

  useLayerEvents(
    layerId,
    {
      onHover: (event, feature) => {
        let value =
          attribute.attributeType !== ImportType.ElectroConductivity
            ? feature?.properties![attribute.name]
            : attribute.isCustom &&
              attribute.attributeType === ImportType.ElectroConductivity
            ? feature?.properties?.custom_properties![attribute.key]
            : feature?.properties![attribute.key];

        if (
          !value &&
          typeof feature?.properties?.['zone_name'] === 'string' &&
          feature?.properties?.['zone_name'] === 'undefined'
        ) {
          value = t('productivity_zones.zone_number.undefined');
        }
        const info = feature
          ? {
              id: feature.id as string | number,
              value,
              lng: event.lngLat.lng,
              lat: event.lngLat.lat,
            }
          : null;
        setHoveredInfo(info);
      },
    },
    [attribute],
  );

  return (
    <>
      {!hoveredInfo && (
        <InteractiveVectorTooltip
          attribute={attribute}
          fieldSeasonGeom={fieldsSeasonGeom}
        />
      )}

      {hoveredInfo && hoveredInfo.value && (
        <Popup
          closeOnClick={false}
          className={classNames.pointTooltip}
          closeButton={false}
          offset={[0, -4]}
          longitude={hoveredInfo.lng}
          latitude={hoveredInfo.lat}
        >
          <div className={classNames.popupValue}>{hoveredInfo.value}</div>
        </Popup>
      )}
    </>
  );
};

export default ResultVectorHoverLayer;
