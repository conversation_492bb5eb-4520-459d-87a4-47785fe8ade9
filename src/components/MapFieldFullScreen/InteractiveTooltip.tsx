import React, { useMemo } from 'react';
import { Popup } from 'react-map-gl';
import { useTranslation } from 'react-i18next';
import { useStore } from 'effector-react';
import { TypedArray } from 'geotiff/dist-module/geotiff';

import { hoveredPosition$ } from 'models/map';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { calculateRelativePosition } from 'features/raster-layer/utils';
import {
  BBox,
  Polygon,
  feature,
  featureCollection,
  point,
} from '@turf/helpers';
import { RasterPixelData, TiffData } from 'features/raster-layer/models/types';

import classNames from 'components/MapFieldFullScreen/HoverTooltip.module.css';
import { FieldSeason } from 'models/fieldSeasons';
import { getPixelData } from '../../utils/vra';

type Props = {
  bbox: BBox;
  valueFormatter?: (val: RasterPixelData) => string | { key: string } | null;
  data: TiffData;
  fieldSeason: FieldSeason;
};

const InteractiveTooltip = ({
  data,
  valueFormatter,
  bbox,
  fieldSeason,
}: Props) => {
  const { t } = useTranslation();
  const hoveredPosition = useStore(hoveredPosition$);

  const fieldGeom = useMemo(() => {
    if (fieldSeason.geom) {
      const geomFeature = feature(fieldSeason.geom);
      return featureCollection([geomFeature]);
    }
    return featureCollection([]);
  }, [fieldSeason.geom]);

  const isHovered = useMemo(() => {
    const hoverPoint = point([
      hoveredPosition.longitude,
      hoveredPosition.latitude,
    ]);

    return fieldGeom.features.some((geomFeature) =>
      booleanPointInPolygon(hoverPoint, geomFeature as unknown as Polygon),
    );
  }, [hoveredPosition, fieldGeom]);

  const hoveredPoint = calculateRelativePosition({
    lat: hoveredPosition.latitude,
    lng: hoveredPosition.longitude,
    bbox,
  });

  const hoveredValue = useMemo((): string | { key: string } | null => {
    if (!data || !hoveredPoint) {
      return null;
    }

    const x = Math.floor(hoveredPoint.x * data.width);
    const y = Math.floor(hoveredPoint.y * data.height);
    const pixelData = getPixelData(data.width, data.data as TypedArray[], x, y);

    if (!pixelData) {
      return null;
    }

    if (isNaN(pixelData[0])) {
      return t('fields.ndvi.no_data');
    }

    if (valueFormatter) {
      return valueFormatter(pixelData);
    }
    return pixelData[0].toFixed(2);
  }, [data, hoveredPoint, valueFormatter, t]);

  if (!hoveredValue || !isHovered) {
    return null;
  }

  return (
    <Popup
      closeOnClick={false}
      className={classNames.pointTooltip}
      closeButton={false}
      offset={[0, -4]}
      {...hoveredPosition}
    >
      <div className={classNames.popupValue}>
        {typeof hoveredValue === 'string' ? hoveredValue : t(hoveredValue?.key)}
      </div>
    </Popup>
  );
};

export default InteractiveTooltip;
