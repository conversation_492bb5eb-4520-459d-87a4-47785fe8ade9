import React from 'react';
import { FeatureCollection, MultiPolygon } from '@turf/helpers';

import { FieldSeason } from 'models/fieldSeasons';
import { MachineryOperationAttribute } from 'features/machinery/models/types';
import { ImportType } from 'features/import-files/models/types';
import { ZoneName } from 'models/vraMaps';

import SoilSamplingResultsLayer from 'features/soil-sampling-results/SoilSamplingResultsLayer/SoilSamplingResultsLayer';
import HarvestingLayer from 'features/machinery/HarvestingLayer';
import ElectroConductivityLayer from 'features/electroconductivity/ElectroConductivityLayer';

type MapFieldLayersProps = {
  rulerEnabled?: boolean;
  fieldSeason: FieldSeason;
  selectedLayer: string | null;
  operation?: MachineryOperationAttribute;
  attributeType?: ImportType | null;
  selectedResultUuid?: string;
  selectedEcUuid?: string;
  selectedHarvestAttribute?: string | null;
  undefinedZones?: FeatureCollection<
    MultiPolygon,
    {
      area_ha: number;
      zone_name: ZoneName;
    }
  > | null;
  beforeId?: string;
  hideBorders?: boolean;
};

const MapFieldLayers = ({
  selectedLayer,
  fieldSeason,
  rulerEnabled,
  operation,
  attributeType,
  selectedResultUuid,
  selectedEcUuid,
  selectedHarvestAttribute,
  beforeId,
  hideBorders,
}: MapFieldLayersProps) => {
  return (
    <>
      {selectedResultUuid && attributeType === ImportType.SoilSampling && (
        <SoilSamplingResultsLayer
          fieldSeason={fieldSeason}
          layer={selectedLayer}
          resultUuid={selectedResultUuid}
          beforeId={beforeId}
          withoutHover={rulerEnabled}
        />
      )}
      {selectedEcUuid && attributeType === ImportType.ElectroConductivity && (
        <ElectroConductivityLayer
          fieldSeason={fieldSeason}
          layer={selectedLayer}
          resultUuid={selectedEcUuid}
          beforeId={beforeId}
          hideBorders={hideBorders}
          withoutHover={rulerEnabled}
        />
      )}
      {selectedHarvestAttribute &&
        operation &&
        Object.keys(operation.legend).includes(selectedHarvestAttribute!) && (
          <HarvestingLayer
            fieldSeason={fieldSeason}
            operation={operation}
            selectedAttribute={selectedHarvestAttribute}
            withoutHover={rulerEnabled}
          />
        )}
    </>
  );
};

export default MapFieldLayers;
