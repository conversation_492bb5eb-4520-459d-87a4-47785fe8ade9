.sidebar {
  width: 240px;
  height: 100%;
  overflow-y: auto;
  border-radius: 8px;
  background-color: var(--color-black-90);
  padding: 10px 0;
}

.menu {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.groupName {
  color: var(--color-grey-100);
  padding: 12px 8px;
}

.link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-black-90);
  color: var(--color-white)
}

.disabledLayer {
  opacity: 0.5;
}

.disabledLayer:hover {
  background-color: inherit;
}

.iconButton {
  transform: rotate(90deg);
}

.menuLink {
  min-width: 160px;
  display: flex;
  align-items: center;
  color: var(--color-black);
  padding: 6px 12px;
}

.menuLinkText {
  display: flex;
  align-items: center;
  gap: 4px;
}