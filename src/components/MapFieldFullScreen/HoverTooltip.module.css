.pointTooltip {
  background-color: transparent;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pointTooltip :global(.mapboxgl-popup-tip) {
  display: none;
}

.pointTooltip :global(.mapboxgl-popup-content) {
  background-color: transparent;
  box-shadow: none;
  padding: 5px;
  pointer-events: none;
}

.popupValue {
  background-color: var(--color-black);
  width: min-content;
  border-radius: 4px;
  pointer-events: none;
  padding: 2px 6px;
  white-space: nowrap;

  font-weight: 500;
  font-size: 14px;

  color: white;
}

.popupValue::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  margin-left: -10px;
  border-width: 10px;
  border-style: solid;
  border-color: var(--color-black) transparent transparent transparent;
}
