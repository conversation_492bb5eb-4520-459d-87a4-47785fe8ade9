import React, { useMemo } from 'react';
import { Popup } from 'react-map-gl';
import { useTranslation } from 'react-i18next';
import { useStore } from 'effector-react';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { Feature, MultiPolygon, Polygon, point, Geometry } from '@turf/helpers';

import { hoveredPosition$ } from 'models/map';
import { SoilSamplingResultAttribute } from 'features/soil-sampling-results/models/types';
import { ElectroConductivityResultAttribute } from 'features/electroconductivity/models/types';
import { ImportType } from 'features/import-files/models/types';
import { useAttributeDataWithUndefined } from 'features/soil-sampling-results/utils';

import classNames from 'components/MapFieldFullScreen/HoverTooltip.module.css';

type Props = {
  attribute: SoilSamplingResultAttribute | ElectroConductivityResultAttribute;
  fieldSeasonGeom?: Geometry;
};

const getFeatureValueByAttributeType = (
  attribute: SoilSamplingResultAttribute | ElectroConductivityResultAttribute,
  hoveredFeature: Feature<Polygon | MultiPolygon>,
) => {
  switch (attribute.attributeType) {
    case ImportType.ElectroConductivity:
      return attribute.isCustom
        ? hoveredFeature?.properties?.custom_properties?.[attribute.key] || null
        : hoveredFeature?.properties?.[attribute.key] || null;
    case ImportType.SoilSampling:
      return hoveredFeature?.properties?.[attribute.name];
    default:
      return null;
  }
};

const InteractiveVectorTooltip = ({ attribute, fieldSeasonGeom }: Props) => {
  const { t } = useTranslation();
  const hoveredPosition = useStore(hoveredPosition$);
  const hoverPoint = point([
    hoveredPosition.longitude,
    hoveredPosition.latitude,
  ]);
  const attributeData = useAttributeDataWithUndefined(
    attribute.data,
    fieldSeasonGeom,
  );

  const hoveredFeature = useMemo(() => {
    const checkPointInPolygon = (feature: Feature) => {
      switch (feature.geometry?.type) {
        case 'Polygon':
        case 'MultiPolygon':
          return booleanPointInPolygon(
            hoverPoint,
            feature as Feature<Polygon | MultiPolygon>,
          );
        default:
          return false;
      }
    };
    const dataFromAttribute = attributeData.features.filter(
      (feature: Feature) => feature.properties?.zone_name !== 'undefined',
    );
    const undefinedData = attributeData.features.filter(
      (feature: Feature) => feature.properties?.zone_name === 'undefined',
    );
    const feature = dataFromAttribute.find(checkPointInPolygon);
    if (feature) {
      return feature;
    }
    const featureWithUndefined = undefinedData.find(checkPointInPolygon);
    return featureWithUndefined;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hoveredPosition, attributeData]);

  const hoveredValue = useMemo((): string | { key: string } | null => {
    if (!hoveredFeature) {
      return null;
    }

    const dataFromAttribute = getFeatureValueByAttributeType(
      attribute,
      hoveredFeature as Feature<Polygon | MultiPolygon>,
    );
    const undefinedData = hoveredFeature.properties?.['zone_name'];

    if (dataFromAttribute) {
      return String(dataFromAttribute);
    }
    if (
      undefinedData &&
      typeof undefinedData === 'string' &&
      undefinedData === 'undefined'
    ) {
      return { key: 'productivity_zones.zone_number.undefined' };
    }
    return null;
  }, [hoveredFeature, attribute]);

  if (!hoveredValue) {
    return null;
  }

  return (
    <Popup
      closeOnClick={false}
      className={classNames.pointTooltip}
      closeButton={false}
      offset={[0, -4]}
      {...hoveredPosition}
    >
      <div className={classNames.popupValue}>
        {typeof hoveredValue === 'string' ? hoveredValue : t(hoveredValue.key)}
      </div>
    </Popup>
  );
};

export default InteractiveVectorTooltip;
