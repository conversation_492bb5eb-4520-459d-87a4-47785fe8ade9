import React, { FC, useEffect, useMemo, useState } from 'react';
import { useStore } from 'effector-react';
import { useTranslation } from 'react-i18next';
import distance from '@turf/distance';
import { Feature, FeatureCollection, LineString, Polygon } from '@turf/helpers';

import { FieldSeason, FieldMapLayer } from 'models/fieldSeasons';
import { hoveredPosition$, LayerMode, ViewState } from 'models/map';
import { formatUnit } from 'utils/units';
import { unitSystem$ } from 'models/settings';
import { formatFieldTitle } from 'utils/fieldSeasons';
import MapMeasurePanel from 'features/map-measure/MapMeasurePanel/MapMeasurePanel';
import { measureDrawModeName } from 'features/map-measure/MapMeasureRulerMode';
import { getFeatureMeasures } from 'features/map-measure/utils';
import { useOutsideClickTrigger } from 'utils/use-outside-click';

import Button from 'components/Button/Button';
import MapControls from 'components/MapControls/MapControls';
import { Modal } from 'components/Modal';
import PositionTo from 'components/PositionTo/PositionTo';
import { Text } from 'components/Text';
import MapFieldInstance from './MapFieldInstance';
import MapFieldSidebar from './MapFieldSidebar';

import { ImportType } from 'features/import-files/models/types';

import classNames from './MapFieldFullScreen.module.css';
import { TiffData } from '../../features/raster-layer/models/types';

type Props = {
  fieldSeason: FieldSeason;
  defaultLayers: FieldMapLayer[];
  defaultAttributeTypes: (ImportType | null)[];
  defaultIsPlanetSources: boolean[];
  resultUuid?: string;
  operationUuid?: string;
  harvestAttr?: string;
  zoneCount?: number;
  ndviTiffData?: TiffData | null;
  onClose?: () => void;
};

type MapDrawState = {
  features: FeatureCollection;
  mode: string;
};

const MapFieldFullScreen: FC<Props> = ({
  fieldSeason,
  defaultLayers,
  defaultAttributeTypes,
  defaultIsPlanetSources,
  resultUuid,
  operationUuid,
  harvestAttr,
  zoneCount,
  ndviTiffData,
  onClose,
}) => {
  const { t, i18n } = useTranslation();
  const unitSystem = useStore(unitSystem$);
  const [layerMode, setLayerMode] = useState<LayerMode>(LayerMode.mode2D);
  const [viewState, setViewState] = useState<ViewState | null>(null);
  const [layers, setLayers] = useState<FieldMapLayer[]>(defaultLayers);
  const [attributeTypes, setAttributeTypes] = useState<(ImportType | null)[]>(
    defaultAttributeTypes,
  );
  const [isPlanetSources, setIsPlanetSources] = useState<boolean[]>(
    defaultIsPlanetSources,
  );
  const [mapDrawEnabled, setMapDrawEnabled] = useState(false);
  const [mapDrawState, setMapDrawState] = useState<MapDrawState | null>(null);

  useEffect(() => {
    if (!mapDrawEnabled) {
      setMapDrawState(null);
    }
  }, [mapDrawEnabled]);

  const onOutsideClick = () => {
    if (mapDrawEnabled) {
      setMapDrawState(
        mapDrawState ? { ...mapDrawState, mode: 'simple_select' } : null,
      );
    }
  };
  const { ref } = useOutsideClickTrigger<HTMLDivElement>(onOutsideClick);

  const handleSetViewState = (partialViewState: Partial<ViewState>) => {
    setViewState({
      ...viewState,
      ...partialViewState,
    } as ViewState);
  };
  const setLayer = ({
    layer,
    index,
    attributeType,
    isPlanet,
  }: {
    layer: FieldMapLayer;
    index: number;
    attributeType: ImportType | null;
    isPlanet?: boolean;
  }) => {
    setAttributeTypes((state) =>
      state.map((l, i) => (i === index ? attributeType : l)),
    );
    setLayers((state) => state.map((l, i) => (i === index ? layer : l)));
    setIsPlanetSources((state) =>
      state.map((l, i) => (i === index ? isPlanet ?? false : l)),
    );
  };
  const onSplit = () => {
    if (layers.length === 1) {
      setAttributeTypes((state) => [...state, ...state]);
      setLayers((state) => [...state, ...state]);
      setIsPlanetSources((state) => [...state, ...state]);
    } else {
      setAttributeTypes((state) => state.filter((_, i) => i === 0));
      setLayers((state) => state.filter((_, i) => i === 0));
      setIsPlanetSources((state) => state.filter((_, i) => i === 0));
    }
  };

  if (layers.length === 0) {
    return null;
  }

  return (
    <Modal open className={classNames.fullscreen}>
      <div className={classNames.container}>
        <div className={classNames.title}>
          <div className={classNames.fieldInfo}>
            <Text weight={'medium'} size={16}>
              {formatFieldTitle(i18n, fieldSeason)}
            </Text>
            <Text className={classNames.fieldInfoArea} size={12}>
              {formatUnit(i18n, unitSystem, fieldSeason.area, 'area', 'ha')}
            </Text>
          </div>
          <div>
            <Button
              theme="map"
              size="small"
              icon="Cross"
              iconColor="var(--color-white)"
              onClick={onClose}
            >
              {t('field-fullscreen-widget.close')}
            </Button>
          </div>
        </div>

        <div ref={ref} className={classNames.mapContainer}>
          {layers.length === 1 && (
            <MapFieldSidebar
              selectedLayer={layers[0]!}
              attributeType={attributeTypes?.[0] || null}
              isPlanet={isPlanetSources[0] || false}
              setSelectedLayer={setLayer}
              fieldSeason={fieldSeason}
            />
          )}
          {layers.map((layer, index) => (
            <div key={index} className={classNames.cell}>
              <MapFieldInstance
                instanceId={index.toString()}
                bbox={fieldSeason.bbox!}
                viewState={viewState}
                onSetViewState={handleSetViewState}
                fieldSeason={fieldSeason}
                selectedLayer={layer}
                setSelectedLayer={setLayer}
                attributeType={attributeTypes?.[index]}
                isPlanet={isPlanetSources[index] || false}
                defaultResultUuid={resultUuid}
                defaultOperationUuid={operationUuid}
                defaultHarvestAttr={harvestAttr}
                layerMode={layerMode}
                zoneCount={zoneCount}
                isSplitView={layers.length === 2}
                compareLayer={
                  layers.length === 2 ? layers[1 - index] : undefined
                }
                rulerEnabled={mapDrawEnabled}
                drawState={mapDrawState}
                onDrawStateChange={setMapDrawState}
                ndviTiffData={ndviTiffData}
              />
            </div>
          ))}

          <PositionTo placement={'right center'} offsetX={15}>
            <MapControls
              onSetLayerMode={setLayerMode}
              layerMode={layerMode}
              isSplit={layers.length === 2}
              onSplit={defaultLayers.length > 1 ? undefined : onSplit}
              onZoomBy={(value) =>
                handleSetViewState({ zoom: viewState!.zoom + value })
              }
              onRulerToggle={() => {
                setMapDrawEnabled(!mapDrawEnabled);
              }}
              rulerEnabled={mapDrawEnabled}
            />
          </PositionTo>
          <PositionTo
            placement="center bottom"
            offsetY={10}
            className={classNames.measurePanel}
          >
            <MapFieldFullScreenMeasurePanel
              mapDrawEnabled={mapDrawEnabled}
              mapDrawState={mapDrawState}
              setMapDrawEnabled={setMapDrawEnabled}
            />
          </PositionTo>
        </div>
      </div>
    </Modal>
  );
};

const MapFieldFullScreenMeasurePanel = ({
  mapDrawEnabled,
  mapDrawState,
  setMapDrawEnabled,
}: {
  mapDrawState: MapDrawState | null;
  mapDrawEnabled: boolean;
  setMapDrawEnabled: (val: boolean) => void;
}) => {
  const hoveredPosition = useStore(hoveredPosition$);
  const drawMeasures = useMemo(() => {
    const hasDrawState = !!mapDrawState?.features?.features.length;
    if (!mapDrawEnabled || !hasDrawState) return null;
    return getFeatureMeasures(
      mapDrawState.features.features[0] as Feature<Polygon | LineString>,
    );
  }, [mapDrawState, mapDrawEnabled]);

  const currentDistance = useMemo(() => {
    if (!mapDrawState || mapDrawState.mode === measureDrawModeName) return 0;
    const feature = mapDrawState.features.features?.[0] as
      | Feature<LineString>
      | undefined;
    if (
      !feature ||
      feature.geometry.coordinates.length === 0 ||
      mapDrawState.mode !== measureDrawModeName ||
      feature.geometry.type !== 'LineString'
    ) {
      return 0;
    }

    const lastPoint =
      feature.geometry.coordinates[feature.geometry.coordinates.length - 1]!;
    const potentialPoint = [
      hoveredPosition.longitude,
      hoveredPosition.latitude,
    ];
    return distance(lastPoint, potentialPoint, {
      units: 'meters',
    });
  }, [mapDrawState, hoveredPosition]);

  if (!mapDrawEnabled) return null;

  return (
    <MapMeasurePanel
      drawMeasures={{ ...drawMeasures, currentDistance }}
      onClose={() => setMapDrawEnabled(false)}
    />
  );
};

export default MapFieldFullScreen;
