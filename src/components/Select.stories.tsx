import { styled } from '@linaria/react';
import { Meta, <PERSON> } from '@storybook/react';
import { format } from 'date-fns';

import Select, { Props } from './Select';

export default {
  title: 'Components/Select',
  component: Select,
} as Meta;

const Wrapper = styled.div`
  width: 200px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 16px;
`;

export const Default: Story<Props> = (props) => (
  <Wrapper>
    <Select>Default</Select>
    <Select disabled>Disabled</Select>
    <Select icon="CalendarSeasons">{format(new Date(), 'dd-MM-yyyy')}</Select>
    <Select size="small" icon="CalendarSeasons">
      Small
    </Select>
  </Wrapper>
);
