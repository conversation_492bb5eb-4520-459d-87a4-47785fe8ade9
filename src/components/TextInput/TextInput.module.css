.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.theme__default {
  background-color: white;
  color: var(--color-black);
  border-color: var(--color-grey-50);
}
.theme__default.wrapper__focused {
  border-color: var(--color-black);
}
.theme__default.wrapper__disabled {
  background-color: var(--color-grey-10);
  border-color: var(--color-grey-30);
  color: var(--color-grey-100);
}
.theme__default .input:disabled::placeholder {
  color: var(--color-grey-50);
}
.theme__default .input:disabled::-moz-placeholder {
  color: var(--color-grey-50);
}
.theme__default .input:disabled::-ms-input-placeholder {
  color: var(--color-grey-50);
}

.theme__map {
  background-color: var(--color-black);
  color: white;
  border-color: var(--color-black);
}
.theme__map.wrapper__focused {
  background-color: var(--color-black-90);
  border-color: var(--color-black-90);
}
.theme__map.wrapper__disabled {
  border-color: var(--color-grey-10);
  color: var(--color-grey-70);
}
.theme__map input {
  color: white;
}
.theme__map .search {
  color: white;
}

.wrapper {
  border-width: 1px;
  border-style: solid;
  border-radius: 8px;
  padding: 0 3px 0 11px;
  height: 40px;
  transition: color 0.3s, border-color 0.3s, background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.wrapper:hover {
  cursor: text;
}

.wrapper:disabled:hover {
  cursor: default;
}

.wrapper__error {
  border-color: var(--color-red);
}
.wrapper__error .input {
  color: var(--color-red);
}

.wrapper__rounded {
  border-radius: 20px;
}

.labelWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search {
  display: flex;
  align-items: center;
  color: var(--color-grey-100);
}

.suffix {
  font-size: 14px;
  line-height: 16px;
  color: var(--color-black-light);
  white-space: pre;
  padding-right: 5px;
}

.input {
  border: none;
  padding: 11px 0;
  margin: 0;
  display: block;
  max-width: none;
  width: 100%;
  font-size: 14px;
  line-height: 16px;
  flex-grow: 1;
  background: transparent;
  transition: color 0.3s;
}

.input:focus {
  outline: none;
}
.input::placeholder {
  color: var(--color-grey-100);
}
.input::-moz-placeholder {
  color: var(--color-grey-100);
}
.input::-ms-input-placeholder {
  color: var(--color-grey-100);
}
.input:disabled {
  background: transparent;
  color: var(--color-grey-100);
}

.error {
  margin-top: 4px;
  margin-bottom: 4px;
  color: var(--color-red);
  font-size: 12px;
  line-height: 16px;
}
