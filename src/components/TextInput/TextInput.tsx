import {
  ForwardedRef,
  InputHTMLAttributes,
  FocusEvent,
  forwardRef,
  useState,
  EventHandler,
  ChangeEvent,
  ReactNode,
  useEffect,
  useRef,
} from 'react';
import Cleave from 'cleave.js/react';
import { ReactInstanceWithCleave } from 'cleave.js/react/props';
import cx from 'classnames';
import { TFunctionResult } from 'i18next';

import FormLabel from 'components/FormLabel';
import SvgIcon from 'components/SvgIcon';
import Button from 'components/Button/Button';
import HelpTooltip from 'components/Tooltip/HelpTooltip';

import classNames from './TextInput.module.css';

const isNumber = (v: any) => !isNaN(parseFloat(v)) && isFinite(v);

interface InputChangeEvent<T> extends ChangeEvent<T> {
  target: { rawValue?: string } & EventTarget & T;
}

export interface Props extends InputHTMLAttributes<HTMLInputElement> {
  theme?: 'default' | 'map';
  rounded?: boolean;
  label?: string;
  labelHint?: string;
  labelRight?: ReactNode;
  suffix?: ReactNode;
  numeric?: boolean;
  groupThousands?: boolean;
  thousandsDelimiter?: string;
  decimalDelimiter?: string;
  decimalScale?: number;
  positiveOnly?: boolean;
  clearable?: boolean;
  withSearchIcon?: boolean;
  onChange?: EventHandler<InputChangeEvent<HTMLInputElement>>;
  onChangeValue?: (v: string) => void;
  onClear?: () => void;
  error?: TFunctionResult | boolean;
  customClassName?: string;
}

const TextInput = forwardRef(
  (
    {
      theme = 'default',
      rounded,
      label,
      labelHint,
      labelRight,
      suffix,
      value,
      onClear,
      disabled,
      numeric,
      error,
      groupThousands = true,
      thousandsDelimiter = ' ',
      decimalScale = 2,
      decimalDelimiter = '.',
      positiveOnly = false,
      clearable = false,
      withSearchIcon = false,
      customClassName,
      onChangeValue,
      ...otherProps
    }: Props,
    ref: ForwardedRef<HTMLInputElement>,
  ) => {
    const [focused, setFocused] = useState(false);
    // for numeric input
    // The only case of using "value" attribute is to pass it as the default value in initialization.
    // https://github.com/nosir/cleave.js/blob/master/doc/reactjs-component-usage.md#how-to-update-raw-value
    const innerValueRef = useRef(value);
    const [cleave, setCleave] = useState<ReactInstanceWithCleave | null>(null);

    useEffect(() => {
      // update innerValue, when value changes (only for numeric)
      if (value?.toString() !== innerValueRef.current && cleave) {
        cleave.setRawValue(value?.toString() || '');
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const onInit = (c: ReactInstanceWithCleave) => {
      setCleave(c);
    };

    const onFocus = (e: FocusEvent<HTMLInputElement>) => {
      otherProps.onFocus?.(e);
      setFocused(true);
    };

    const onBlur = (e: FocusEvent<HTMLInputElement>) => {
      otherProps.onBlur?.(e);
      setFocused(false);
    };

    return (
      <label className={classNames.container}>
        {!!label && (
          <div className={classNames.labelWrapper}>
            <FormLabel>{label}</FormLabel>
            {!!labelRight && <FormLabel>{labelRight}</FormLabel>}
            {!!labelHint && (
              <HelpTooltip icon="HelpOutline" placement="auto">
                {labelHint}
              </HelpTooltip>
            )}
          </div>
        )}
        <div
          className={cx(
            classNames.wrapper,
            rounded && classNames.wrapper__rounded,
            focused && classNames.wrapper__focused,
            !!error && classNames.wrapper__error,
            disabled && classNames.wrapper__disabled,
            classNames[`theme__${theme}`],
            customClassName && customClassName,
          )}
        >
          {!!withSearchIcon && (
            <span className={classNames.search}>
              <SvgIcon width={16} height={16} name={'Search'} />
            </span>
          )}
          {numeric ? (
            <Cleave
              {...otherProps}
              htmlRef={(inputRef) => (ref = inputRef)}
              options={{
                numeral: true,
                delimiter: thousandsDelimiter,
                numeralThousandsGroupStyle: groupThousands
                  ? 'thousand'
                  : 'none',
                numeralDecimalMark: decimalDelimiter,
                numeralDecimalScale: decimalScale,
                numeralPositiveOnly: positiveOnly,
              }}
              onFocus={onFocus}
              value={value}
              onBlur={onBlur}
              onInit={onInit}
              className={classNames.input}
              disabled={disabled}
              onChange={(event) => {
                otherProps.onChange?.(event);
                const value = event.target.rawValue;
                innerValueRef.current = isNumber(value) ? value : '0';
                onChangeValue?.(isNumber(value) ? value : '0');
              }}
            />
          ) : (
            <input
              ref={ref}
              type="text"
              {...otherProps}
              className={classNames.input}
              value={value}
              disabled={disabled}
              onFocus={onFocus}
              onBlur={onBlur}
              onChange={(event) => {
                otherProps.onChange?.(event);
                onChangeValue?.(event.target.value);
              }}
            />
          )}
          {!suffix && clearable && !!value && (
            <Button
              icon="Cross"
              size="small"
              rounded={rounded}
              theme={theme === 'map' ? 'toast' : 'toolbar'}
              onClick={onClear}
            />
          )}
          {!!suffix && !clearable && (
            <span className={classNames.suffix}>{suffix}</span>
          )}
        </div>
        {!!error && typeof error === 'string' && (
          <span className={classNames.error}>{error}</span>
        )}
      </label>
    );
  },
);

export default TextInput;
