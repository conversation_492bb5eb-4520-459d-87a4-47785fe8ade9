import TextInputExample from './TextInput';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';
import { useState } from 'react';

export default {
  title: 'Components/TextInput',
  component: TextInputExample,
  argTypes: {
    label: {
      description: 'Prop value will be shown as label above field',
    },
    suffix: {
      description: 'Shows suffix text inside field',
    },
    value: {
      description:
        'Sets field value. Since this is a controlled component, `onChange` is required',
    },
    disabled: {
      description: 'Disables field',
    },
    numeric: {
      description: 'Makes field numeric with thousands delimiter',
    },
    groupThousands: {
      description: 'Switch thousand grouping',
    },
    thousandsDelimiter: {
      description: 'Sets thousands delimiter. Default is space',
    },
    decimalDelimiter: {
      description: 'Sets decimal delimiter. Default is `.`',
    },
    positiveOnly: {
      description: 'Allows entering only positive numbers',
    },
    clearable: {
      description:
        'If field has value, clear button will be shown. `onClear` must be passed',
    },
    withSearchIcon: {
      description: 'Adds search icon on the left side',
    },
    onClear: {
      description: '<PERSON><PERSON> called by pressing clear button',
    },
    onChange: {
      description: 'Input change handler. Receives ChangeEvent as an argument',
    },
  },
  args: {
    label: '',
    suffix: '',
    value: 'Initial value',
    disabled: false,
    numeric: false,
    groupThousands: true,
    thousandsDelimiter: ' ',
    decimalDelimiter: '.',
    positiveOnly: false,
    withSearchIcon: false,
    clearable: true,
    error: '',
  },
} as Meta;

const InputWrapper = styled.div`
  width: 200px;
  margin-left: auto;
  margin-right: auto;
`;
const Gap = styled.div`
  height: 20px;
`;

export const Default: Story = (args) => {
  const [value, setValue] = useState('');
  return (
    <InputWrapper>
      <TextInputExample
        {...args}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
        placeholder={'Input text...'}
        onClear={() => setValue('')}
      />
    </InputWrapper>
  );
};

export const Theme: Story = (args) => {
  const [value, setValue] = useState('');
  return (
    <InputWrapper>
      <TextInputExample
        {...args}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
        placeholder={'Default'}
        onClear={() => setValue('')}
      />
      <Gap />
      <TextInputExample
        {...args}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
        placeholder={'Map'}
        theme="map"
        withSearchIcon
        clearable
        onClear={() => setValue('')}
      />
      <Gap />
      <TextInputExample
        {...args}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
        placeholder={'Default Rounded'}
        onClear={() => setValue('')}
        rounded
      />
      <Gap />
      <TextInputExample
        {...args}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
        placeholder={'Map Rounded'}
        theme="map"
        withSearchIcon
        clearable
        onClear={() => setValue('')}
        rounded
      />
      <Gap />
      <TextInputExample
        {...args}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
        placeholder={'Default Disabled'}
        onClear={() => setValue('')}
        disabled
      />
      <Gap />
      <TextInputExample
        {...args}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
        placeholder={'Map Disabled'}
        theme="map"
        withSearchIcon
        clearable
        onClear={() => setValue('')}
        disabled
      />
    </InputWrapper>
  );
};

export const WithLabel: Story = (args) => {
  const [value, setValue] = useState('');
  return (
    <InputWrapper>
      <TextInputExample
        {...args}
        label={'First name'}
        onChange={(e) => setValue(e.target.value)}
        value={value === undefined ? args.value : value}
      />
    </InputWrapper>
  );
};

export const WithSuffix: Story = (args) => {
  const [value, setValue] = useState('100');
  return (
    <InputWrapper>
      <TextInputExample
        numeric={args.numeric}
        suffix={'kg/ha'}
        onChange={(e) => setValue(e.target.value)}
        value={value}
      />
    </InputWrapper>
  );
};

export const Numeric: Story = (args) => {
  const [value, setValue] = useState(100);
  return (
    <InputWrapper>
      <TextInputExample
        {...args}
        numeric
        positiveOnly
        suffix={'kg/ha'}
        value={`${value}`}
        onChangeValue={(v) => setValue(+v)}
      />
    </InputWrapper>
  );
};

export const WithError: Story = (args) => {
  const [value, setValue] = useState('100');
  return (
    <InputWrapper>
      <TextInputExample
        {...args}
        error="Fffffuuuuu"
        suffix={'kg/ha'}
        onChange={(e) => setValue(e.target.value)}
        value={value}
      />
    </InputWrapper>
  );
};
