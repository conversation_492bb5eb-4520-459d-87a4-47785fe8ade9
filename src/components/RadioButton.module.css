.radio {
  box-sizing: border-box;
  width: 18px;
  height: 18px;
  padding: 0;
  margin: 3px;
  border: 1px solid var(--color-grey-70);
  border-radius: 50%;
  appearance: none;
  background-color: white;
  outline: none;
  transition: border-color 0.3s, background-color 0.3s, border-width 0.2s;
}
.radio:checked {
  border-width: 6px;
}
.radio:disabled {
  border-color: var(--color-grey-50);
  cursor: not-allowed;
}
.radio:not(:disabled):checked {
  border-color: var(--color-grey-80);
}
.radio:disabled:not(:checked) {
  background-color: var(--color-grey-10);
}
