import PositionToExample, { Props } from './PositionTo';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';
import Button from 'components/Button/Button';

export default {
  title: 'Components/PositionTo',
  component: PositionToExample,
  args: {
    placement: 'left top',
    offsetX: 0,
    offsetY: 0,
  },
} as Meta;

const Wrapper = styled.div`
  width: 600px;
  height: 500px;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  background: var(--color-grey-30);
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <PositionToExample {...args}>
      <Button size={'normal'} theme="primary">
        Test positioning
      </Button>
    </PositionToExample>
  </Wrapper>
);
