.base {
  position: absolute;
  left: auto;
  top: auto;
  transition: transform 0.3s;
  transform: translate(0, 0);
}

.hc {
  left: 50%;
  transform: translateX(-50%);
}

.hl {
  left: var(--offsetX);
}
:global(.sidebarExpanded) .hl.withSidebarSync {
  transform: translateX(var(--folders-width));
}

:global(.sidebarExpanded) .hc.withSidebarSync {
  transform: translateX(-50%) translateX(calc(var(--folders-width) * 0.5));
}

.hr {
  right: var(--offsetX);
}

.vt {
  top: var(--offsetY);
}

.vb {
  bottom: var(--offsetY);
}

.vc {
  top: 50%;
  transform: translateY(-50%);
}
:global(.sidebarExpanded) .hl.vc.withSidebarSync {
  transform: translateX(var(--folders-width)) translateY(-50%);
}

.hcvc {
  transform: translateX(-50%) translateY(-50%);
}
