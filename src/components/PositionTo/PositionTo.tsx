import { ReactNode } from 'react';
import cx from 'classnames';

import classNames from './PositionTo.module.css';

export type Props = {
  placement: string;
  offsetX?: number;
  offsetY?: number;
  children: ReactNode;
  className?: string;
  withSidebarSync?: boolean;
};

const PositionTo = ({
  placement,
  offsetX = 0,
  offsetY = 0,
  children,
  className,
  withSidebarSync,
}: Props) => {
  const classes = [];
  const style: object = {
    '--offsetX': `${offsetX}px`,
    '--offsetY': `${offsetY}px`,
  };

  const placements = placement.split(' ');
  const placementX = placements[0];
  const placementY = placements.length > 1 ? placements[1] : 'center';

  switch (placementX) {
    case 'left':
      classes.push(classNames.hl);
      break;
    case 'right':
      classes.push(classNames.hr);
      break;
    case 'center':
      classes.push(classNames.hc);
      break;
    default:
      break;
  }

  switch (placementY) {
    case 'top':
      classes.push(classNames.vt);
      break;
    case 'bottom':
      classes.push(classNames.vb);
      break;
    case 'center':
      classes.push(classNames.vc);
      break;
    default:
      break;
  }

  if (placementX === 'center' && placementY === 'center') {
    classes.push(classNames.hcvc);
  }

  return (
    <div
      className={cx(
        classNames.base,
        ...classes,
        className,
        withSidebarSync && classNames.withSidebarSync,
      )}
      style={{ ...style }}
    >
      {children}
    </div>
  );
};

export default PositionTo;
