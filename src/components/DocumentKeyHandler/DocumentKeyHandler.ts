import { Component } from 'react';
import isHotkey from 'is-hotkey';
import { DocumentKeyHandlerProps } from './types';

class DocumentKeyHandler extends Component<DocumentKeyHandlerProps> {
  componentDidMount() {
    document.addEventListener('keydown', this.handleKeyDown);
  }

  componentWillUnmount() {
    document.removeEventListener('keydown', this.handleKeyDown);
  }

  handleKeyDown = (event: KeyboardEvent) => {
    const { hotkey, hotkeys, onPress } = this.props;
    const hotkeysToCheck = hotkeys || [hotkey];
    if (hotkeysToCheck.every((hotkey) => !isHotkey(hotkey, event))) {
      return;
    }
    event.stopPropagation();
    event.preventDefault();
    onPress(event);
  };

  render() {
    return null;
  }
}

export default DocumentKeyHandler;
