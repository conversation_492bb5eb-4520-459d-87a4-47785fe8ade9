.modalCalendar {
  position: absolute;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid rgba(214, 220, 225, 0.5);
  z-index: 1000
}

.modalCalendarDates {
  padding: 12px;
}

.modalCalendarDatesTable {
  width: 100%;
  table-layout: fixed;
}
.modalCalendarDatesTable td {
  padding: 1px 0;
  text-align: center;
}

.modalCalendarDatesTable tr {
  margin-bottom: 5px;
}

.modalCalendarItems {
  display: flex;
}

.modalCalendarItems.animAllow {
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}

.modalCalendarItemsItem {
  flex: 0 0 auto;
  width: 100%;
}

.modalCalendarArea {
  position: relative;
  overflow: hidden;
}

.modalCalendarDatesTitleday {
  width: 44px;
  height: 46px;
  color: var(--color-black-light);
  font-size: 12px;
}

.header {
  display: flex;
  justify-content: space-between;
}

.calendarsContainer {
  display: flex;
}

.calendarItem {
  flex: 1;
  min-width: 328px;
}

.calendarTitle {
  margin-left: 12px;
}

.calendarDayItem {
  width: 44px;
  height: 46px;
  text-align: center;
  border-radius: 30px;
  line-height: 20px;
}

.calendarDayItemContentContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.calendarDayItemContent {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.calendarDayOthermonth {
  color: var(--color-grey-70);
}

.calendarDayDisabled {
  color: var(--color-grey-70);
}

.calendarDayItem:hover .calendarDayItemContentContainer:not(.calendarDaySelected):not(.calendarDayHighlighted) {
  background-color: var(--color-grey-30);
  border-radius: 30px;
}

.calendarDayHighlighted {
  background-color: var(--color-grey-30);
  border-radius: 0;
}

.calendarDaySelected {
  color: var(--color-white);
  background-color: var(--color-black);
  font-weight: 500;
  border-radius: 30px;
}

.calendarDayItemLeftBorder {
  border-top-left-radius: 30px;
  border-bottom-left-radius: 30px;
}

.calendarDayItemRightBorder {
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
}

.cloudFree {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--color-grey-100);
  margin-left: 16px;
  margin-bottom: 12px;
}

.dayContent {
  position: relative;
}

.dayValue {
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--color-green-light);
  font-size: 9px;
}
