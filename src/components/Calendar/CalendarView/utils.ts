import cx from 'classnames';

import classNames from './CalendarView.module.css';

type CalendarDayProps = {
  isHighlighted?: boolean;
  isSelected?: boolean;
};

export const getCalendarDayBorderClassName = ({
  index,
  currentDayProps,
  prevDayProps,
  nextDayProps: nextCalendarDayProps,
}: {
  index: number;
  currentDayProps: CalendarDayProps;
  prevDayProps?: CalendarDayProps;
  nextDayProps?: CalendarDayProps;
}) => {
  let borderRadiusCls: string | undefined;
  const { isHighlighted, isSelected } = currentDayProps;

  const previousDayProps = prevDayProps || {
    isHighlighted: false,
    isSelected: false,
  };
  const nextDayProps = nextCalendarDayProps || {
    isHighlighted: false,
    isSelected: false,
  };

  if (index === 0) {
    borderRadiusCls = classNames.calendarDayItemLeftBorder;
  } else if (index === 6) {
    borderRadiusCls = classNames.calendarDayItemRightBorder;
  } else {
    if (isSelected) {
      if (!previousDayProps.isHighlighted && !nextDayProps.isHighlighted) {
        borderRadiusCls = cx(
          classNames.calendarDayItemLeftBorder,
          classNames.calendarDayItemRightBorder,
        );
      }
    } else if (isHighlighted && !nextDayProps.isHighlighted) {
      borderRadiusCls = classNames.calendarDayItemRightBorder;
    }
  }

  if (!previousDayProps.isHighlighted && !previousDayProps.isSelected) {
    borderRadiusCls = cx(borderRadiusCls, classNames.calendarDayItemLeftBorder);
  }
  if (!nextDayProps.isHighlighted && !nextDayProps.isSelected) {
    borderRadiusCls = cx(
      borderRadiusCls,
      classNames.calendarDayItemRightBorder,
    );
  }
  return borderRadiusCls;
};
