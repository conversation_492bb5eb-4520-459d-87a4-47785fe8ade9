import React, { useEffect, useState, useMemo } from 'react';
import Dayzed, { DateObj } from 'dayzed';
import noop from 'lodash/noop';
import { format, parse, add, startOfWeek, addDays } from 'date-fns';
import { useTranslation } from 'react-i18next';
import cx from 'classnames';

import { toDate, DateFormat, getYearsRange } from 'utils/date';
import { DateLocales } from 'utils/formatters';
import { getCalendarDayBorderClassName } from './utils';
import { CalendarProps } from '../types';

import { Text } from 'components/Text';
import { CalendarNavigation } from '../CalendarNavigation';
import { CalendarHeader } from '../CalendarHeader';

import classNames from './CalendarView.module.css';

type Props = CalendarProps & {
  title?: string;
  value?: string;
  onSelect: (newDate: Date) => void;
};

const CalendarView = ({
  title,
  value,
  minDate,
  maxDate,
  minYear = 2000,
  maxYear = format(add(new Date(), { years: 5 }), 'yyyy'),
  renderFooter,
  openToDate,
  onSelect,
  onMonthClick = noop,
  onYearClick = noop,
  onBackArrowClick = noop,
  onNextArrowClick = noop,
  withRange,
  showOutsideDays = true,
  setHoveredDate,
  getDayProps = () => ({}),
}: Props) => {
  const { i18n } = useTranslation();

  const shortWeekDaysArray = useMemo(() => {
    const locale = DateLocales[i18n.resolvedLanguage];
    const firstDOW = startOfWeek(new Date(), { locale });
    return Array.from(Array(7)).map((_, i) =>
      format(addDays(firstDOW, i), 'eee', { locale }),
    );
  }, [i18n.resolvedLanguage]);

  const [openDate, setOpenDate] = useState(
    value
      ? new Date(value as string)
      : openToDate
      ? parse(openToDate, DateFormat, new Date())
      : new Date(),
  );

  useEffect(() => {
    if (openToDate) {
      setOpenDate(parse(openToDate, DateFormat, new Date()));
    }
  }, [openToDate]);

  const [offset, setOffset] = useState(0);

  useEffect(() => {
    setOffset(0);
  }, [openDate]);

  const selectedDate = value ? parse(value, DateFormat, new Date()) : undefined;

  const handleMouseEnter = (day: Date | null) => {
    setHoveredDate && setHoveredDate(day);
  };
  const yearRange = getYearsRange(minYear, maxYear);

  const onDateSelected = ({ date }: DateObj) => {
    setHoveredDate && setHoveredDate(null);
    onSelect(date);
  };

  return (
    <Dayzed
      offset={offset}
      onOffsetChanged={setOffset}
      showOutsideDays={showOutsideDays}
      selected={selectedDate}
      minDate={toDate(minDate)}
      maxDate={toDate(maxDate)}
      date={openDate}
      onDateSelected={onDateSelected}
      render={({ calendars, getBackProps, getForwardProps, getDateProps }) => {
        if (calendars.length === 0) {
          return null;
        }

        return (
          <div className={classNames.calendarsContainer}>
            {calendars.map((calendar, index) => {
              return (
                <div
                  key={`calendar-${index}`}
                  className={classNames.calendarItem}
                >
                  {!!title && (
                    <Text
                      className={classNames.calendarTitle}
                      size={16}
                      weight="medium"
                    >
                      {title}
                    </Text>
                  )}
                  <div className={classNames.header}>
                    <CalendarHeader
                      calendar={calendar}
                      yearRange={yearRange}
                      onChangeDate={setOpenDate}
                      onChangeMonth={onMonthClick}
                      onChangeYear={onYearClick}
                    />
                    <CalendarNavigation
                      getBackProps={getBackProps}
                      getForwardProps={getForwardProps}
                      calendars={calendars}
                      calendar={calendar}
                      onBackArrowClick={onBackArrowClick}
                      onNextArrowClick={onNextArrowClick}
                    />
                  </div>

                  <div className={classNames.modalCalendarArea}>
                    <div
                      className={cx(
                        classNames.modalCalendarItems,
                        classNames.animAllow,
                      )}
                    >
                      <div className={classNames.modalCalendarItemsItem}>
                        <div className={classNames.modalCalendarDates}>
                          <table className={classNames.modalCalendarDatesTable}>
                            <thead>
                              <tr>
                                {shortWeekDaysArray.map((day, index) => (
                                  <th
                                    key={index}
                                    className={
                                      classNames.modalCalendarDatesTitleday
                                    }
                                  >
                                    {day}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {calendar.weeks.map((week, index) => (
                                <tr key={index}>
                                  {week.map((day, index) => {
                                    if (day === '' || !day) {
                                      return <td key={index} />;
                                    }
                                    const {
                                      isSelectable,
                                      isHighlighted,
                                      isSelected,
                                      content,
                                      value,
                                    } = getDayProps(day);

                                    const borderRadiusCls =
                                      getCalendarDayBorderClassName({
                                        index,
                                        currentDayProps: getDayProps(day),
                                        prevDayProps: week[index - 1]
                                          ? getDayProps(
                                              week[index - 1] as DateObj,
                                            )
                                          : undefined,
                                        nextDayProps: week[index + 1]
                                          ? getDayProps(
                                              week[index + 1] as DateObj,
                                            )
                                          : undefined,
                                      });

                                    return (
                                      <td
                                        key={index}
                                        className={cx(
                                          classNames.calendarDayItem,
                                          (day.prevMonth || day.nextMonth) &&
                                            classNames.calendarDayOthermonth,
                                          !isSelectable &&
                                            classNames.calendarDayDisabled,
                                        )}
                                        onMouseEnter={() =>
                                          withRange &&
                                          handleMouseEnter(day.date)
                                        }
                                        onMouseLeave={() => {
                                          withRange && handleMouseEnter(null);
                                        }}
                                      >
                                        <div
                                          {...(isSelectable
                                            ? getDateProps({ dateObj: day })
                                            : {})}
                                          className={cx(
                                            classNames.calendarDayItemContentContainer,
                                            (isHighlighted || isSelected) &&
                                              classNames.calendarDayHighlighted,
                                            borderRadiusCls,
                                          )}
                                        >
                                          <div
                                            className={cx(
                                              classNames.calendarDayItemContent,
                                              isSelected
                                                ? classNames.calendarDaySelected
                                                : undefined,
                                            )}
                                          >
                                            {!!content && content}
                                            {!content && (
                                              <div
                                                className={
                                                  classNames.dayContent
                                                }
                                              >
                                                {day?.date?.getDate()}
                                                {value && (
                                                  <div
                                                    className={
                                                      classNames.dayValue
                                                    }
                                                  >
                                                    {value.toFixed(2)}
                                                  </div>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      </td>
                                    );
                                  })}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                  {renderFooter && value && renderFooter({ value, onSelect })}
                </div>
              );
            })}
          </div>
        );
      }}
    />
  );
};

export default CalendarView;
