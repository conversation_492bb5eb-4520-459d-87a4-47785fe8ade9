import { format } from 'date-fns';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DateFormat } from 'utils/date';
import { CalendarProps } from '../types';

import { Modal, ModalTitle } from 'components/Modal';
import Button from 'components/Button/Button';
import RangeCalendarView from '../RangeCalendarView/RangeCalendarView';

import classNames from './RangeCalendarModal.module.css';

type Props = CalendarProps & {
  value: [string, string];
  onSelect: (val: [string, string]) => void;
  onClose: () => void;
};

const RangeCalendarModal = (props: Props) => {
  const { t } = useTranslation();
  const [value, setValue] = useState(props.value);

  const onSetValue = (val: [Date, Date]) => {
    const start = format(val[0], DateFormat);
    const end = format(val[1], DateFormat);
    setValue([start, end]);
  };

  return (
    <Modal
      open
      className={classNames.modalContainer}
      onClose={props.onClose}
      showCloseButton
    >
      <div className={classNames.title}>
        <ModalTitle>{t('range-calendar-modal.custom-period.title')}</ModalTitle>
      </div>
      <div className={classNames.modalBody}>
        <RangeCalendarView {...props} value={value} onSelect={onSetValue} />
      </div>
      <div className={classNames.actions}>
        <div>
          <Button
            onClick={(e) => {
              e.preventDefault();
              props.onClose();
            }}
            size="normal"
            icon="Cross"
          >
            {t('range-calendar-modal.cancel')}
          </Button>
        </div>
        <div>
          <Button
            size="normal"
            theme="black"
            icon="CheckMark"
            onClick={(e) => {
              e.preventDefault();
              props.onSelect(value);
              props.onClose();
            }}
          >
            {t('range-calendar-modal.apply')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default RangeCalendarModal;
