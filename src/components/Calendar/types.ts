import React, { FC } from 'react';
import { Calendar, DateObj, RenderProps } from 'dayzed';

export type CalendarDayProps = {
  isSelectable?: boolean;
  isHighlighted?: boolean;
  isSelected?: boolean;
  content?: React.ReactNode;
  value?: number;
};

export type CalendarProps = {
  minDate?: string;
  maxDate?: string;
  minYear?: number;
  maxYear?: string;
  renderFooter?: FC<{
    value: string;
    onSelect: (newDate: Date) => void;
  }>;
  openToDate?: string;
  onMonthClick?: (newMonth: number) => void;
  onYearClick?: (newYear: number) => void;
  onBackArrowClick?: CalendarNavigationProps['onBackArrowClick'];
  onNextArrowClick?: CalendarNavigationProps['onNextArrowClick'];
  withRange?: boolean;
  showOutsideDays?: boolean;
  hoveredDate?: Date | null;
  setHoveredDate?: (date: Date | null) => void;
  getDayProps?: (day: DateObj) => CalendarDayProps;
};

export type CalendarNavigationProps = {
  getBackProps: RenderProps['getBackProps'];
  getForwardProps: RenderProps['getForwardProps'];
  calendars: Calendar[];
  calendar: Calendar;
  onBackArrowClick: (month: number, year: number) => void;
  onNextArrowClick: (month: number, year: number) => void;
};

export type CalendarHeaderProps = {
  calendar: Calendar;
  yearRange: string[];
  onChangeDate: (newDate: Date) => void;
  onChangeMonth?: (newMonth: number) => void;
  onChangeYear?: (newYear: number) => void;
};
