import { VFC } from 'react';
import { useTranslation } from 'react-i18next';

import { DateLocales } from 'utils/formatters';
import { CalendarHeaderProps } from './types';

import DropdownInput from 'components/DropdownInput';

import classNames from './CalendarHeader.module.css';

export const CalendarHeader: VFC<CalendarHeaderProps> = ({
  calendar,
  onChangeDate,
  onChangeMonth,
  onChangeYear,
  yearRange,
}) => {
  const { i18n } = useTranslation();
  const months = [...Array(12).keys()].map((i) => {
    return (
      DateLocales?.[i18n.resolvedLanguage]?.localize?.month(i, {
        width: 'wide',
      }) || ''
    );
  });

  return (
    <div className={classNames.calendarHeader}>
      <div className={classNames.calendarHeaderInner}>
        <DropdownInput
          triggerClassName={classNames.dropdown}
          options={months.map((item, i) => {
            return {
              id: i.toString(),
              label: item,
            };
          })}
          popoverAutoWidth
          value={calendar.month.toString()}
          onChange={(id) => {
            if (!id) return;
            onChangeDate(new Date(calendar.year, Number(id)));
            onChangeMonth?.(Number(id) + 1);
          }}
        />

        <DropdownInput
          triggerClassName={classNames.dropdown}
          options={yearRange.map((item) => {
            return {
              id: item,
              label: item,
            };
          })}
          popoverAutoWidth
          value={calendar.year.toString()}
          onChange={(id) => {
            if (!id) return;
            const year = yearRange.find((year) => year === id);

            if (year) {
              const selectedYear = parseInt(year, 10);
              onChangeDate(new Date(selectedYear, calendar.month));
              onChangeYear?.(selectedYear);
            }
          }}
        />
      </div>
    </div>
  );
};
