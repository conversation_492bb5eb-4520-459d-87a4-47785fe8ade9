import React, { CSSProperties } from 'react';
import { useStore } from 'effector-react';
import { useTranslation } from 'react-i18next';

import { CalendarProps } from './types';
import { fetchPeriodChartNDVIByDateFx } from '../../features/vra/models/ndvi';

import CalendarView from './CalendarView/CalendarView';
import Loader from '../Loader/Loader';

import classNames from './CalendarView/CalendarView.module.css';

type CalendarPopoverProps = {
  style?: CSSProperties;
  value?: string;
  onSelect: (newDate: Date) => void;
} & CalendarProps;

const CalendarPopover = (props: CalendarPopoverProps) => {
  const isGettingData = useStore(fetchPeriodChartNDVIByDateFx.pending);
  const { t } = useTranslation();
  return (
    <div className={classNames.modalCalendar} style={{ ...props.style }}>
      <CalendarView {...props} value={props.value as string} />
      {isGettingData && (
        <div className={classNames.cloudFree}>
          <Loader size={14} color="var(--color-grey-100)" inline loading />
          {t('update-last-dates.updating.cloud-free')}
        </div>
      )}
    </div>
  );
};

export default CalendarPopover;
