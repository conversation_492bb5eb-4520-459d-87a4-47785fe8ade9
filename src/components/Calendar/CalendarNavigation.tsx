import { useEffect, useRef, VFC } from 'react';

import IconButton from 'components/IconButton/IconButton';
import { CalendarNavigationProps } from './types';

import classNames from './CalendarNavigation.module.css';

export const CalendarNavigation: VFC<CalendarNavigationProps> = ({
  getBackProps,
  getForwardProps,
  calendars,
  calendar,
  onBackArrowClick,
  onNextArrowClick,
}) => {
  const monthNav = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (!monthNav.current) {
      return;
    }
    monthNav.current.focus();
  }, []);

  return (
    <div
      ref={monthNav}
      className={classNames.calendarArrows}
      tabIndex={0}
      onKeyDown={(event) => {
        if (event.key === 'ArrowLeft') {
          getBackProps({ calendars }).onClick(event);
        }
        if (event.key === 'ArrowRight') {
          getForwardProps({ calendars }).onClick(event);
        }
      }}
    >
      <IconButton
        hoverColor="var(--color-primary)"
        icon="NavLeft"
        {...getBackProps({ calendars })}
        onClick={(event) => {
          getBackProps({ calendars }).onClick(event);
          const year = calendar.month === 0 ? calendar.year - 1 : calendar.year;
          const month = calendar.month === 0 ? 12 : calendar.month;
          onBackArrowClick(month, year);
        }}
        iconSize={16}
      />

      <IconButton
        hoverColor="var(--color-primary)"
        icon="NavRight"
        {...getForwardProps({ calendars })}
        onClick={(event) => {
          getForwardProps({ calendars }).onClick(event);
          const year =
            calendar.month === 11 ? calendar.year + 1 : calendar.year;
          const month = calendar.month === 11 ? 1 : calendar.month + 2;
          onNextArrowClick(month, year);
        }}
        iconSize={16}
      />
    </div>
  );
};
