import { startOfDay } from 'date-fns';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { CalendarProps } from '../types';
import CalendarView from '../CalendarView/CalendarView';

import classNames from './RangeCalendarView.module.css';

type Props = {
  value: [string, string];
  onSelect: (val: [Date, Date]) => void;
} & CalendarProps;

const RangeCalendarView = ({ value, ...props }: Props) => {
  const { t } = useTranslation();
  const [hover, setHover] = useState<{
    date: Date;
    calendar: 'start' | 'end';
  } | null>(null);
  const [openDates, setOpenDates] = useState<
    [string, string | undefined] | undefined
  >(undefined);

  useEffect(() => {
    if (value) {
      setOpenDates([value[0]!, value[1]]);
    }
  }, [value]);

  const selectedDate = value.map((val) => startOfDay(new Date(val)));
  const [startValue, endValue] = value ?? [];
  const [firstRangeSelectedDate, secondRangeSelectedDate] = selectedDate as [
    Date,
    Date,
  ];

  const isInRange = (targetDate: Date, calendar: 'start' | 'end') => {
    const targetTime = targetDate.getTime();
    const startSelectedTime = firstRangeSelectedDate.getTime();
    const endSelectedTime = secondRangeSelectedDate.getTime();
    const isEndCalendarInRange =
      hover &&
      calendar === 'end' &&
      hover.calendar === 'end' &&
      targetTime >= startSelectedTime &&
      targetTime <= hover.date.getTime();
    const isStartCalendarInRange =
      hover &&
      calendar === 'start' &&
      hover.calendar === 'start' &&
      targetTime <= endSelectedTime &&
      targetTime >= hover.date.getTime();

    return (
      (targetTime >= startSelectedTime && targetTime <= endSelectedTime) ||
      isEndCalendarInRange ||
      isStartCalendarInRange
    );
  };

  return (
    <div className={classNames.rangeCalendarContainer}>
      <div className={classNames.calendarsList}>
        <div className={classNames.calendarView}>
          <CalendarView
            {...props}
            title={t('calendar.title.start-date')}
            openToDate={openDates?.[0]}
            value={startValue}
            setHoveredDate={(date) => {
              if (!date) {
                setHover(null);
                return;
              }
              setHover({ date, calendar: 'start' });
            }}
            onSelect={(date) => {
              let secondDate = secondRangeSelectedDate;
              if (date.getTime() > secondRangeSelectedDate.getTime()) {
                secondDate = date;
              }
              props.onSelect([date, secondDate]);
            }}
            getDayProps={(day) => {
              const isSecondRangeSelectedDate =
                day.date.getTime() === secondRangeSelectedDate.getTime();
              return {
                isSelectable: day.selectable,
                isSelected: day.selected || isSecondRangeSelectedDate,
                isHighlighted: isInRange(day.date, 'start') || undefined,
              };
            }}
          />
        </div>
        <div className={classNames.separator} />
        <div className={classNames.calendarView}>
          <CalendarView
            {...props}
            title={t('calendar.title.end-date')}
            value={endValue}
            openToDate={openDates?.[1]}
            onSelect={(date) => {
              let firstDate = firstRangeSelectedDate;
              if (date.getTime() < firstRangeSelectedDate.getTime()) {
                firstDate = date;
              }
              props.onSelect([firstDate, date]);
            }}
            setHoveredDate={(date) => {
              if (!date) {
                setHover(null);
                return;
              }
              setHover({ date, calendar: 'end' });
            }}
            getDayProps={(day) => {
              const isFirstRangeSelectedDate =
                day.date.getTime() === firstRangeSelectedDate.getTime();
              return {
                isSelectable: day.selectable,
                isSelected: day.selected || isFirstRangeSelectedDate,
                isHighlighted: isInRange(day.date, 'end') || undefined,
              };
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default RangeCalendarView;
