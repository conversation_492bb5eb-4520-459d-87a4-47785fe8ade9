import React from 'react';
import cx from 'classnames';

import classNames from './MenuLink.module.css';

// Type magic from https://gist.github.com/kripod/4434e7cecfdecee160026aee49ea6ee8

type PropsOf<
  E extends keyof JSX.IntrinsicElements | React.JSXElementConstructor<any>,
> = JSX.LibraryManagedAttributes<E, React.ComponentPropsWithRef<E>>;

export type MenuLinkTheme = 'default' | 'danger' | 'dark' | 'noHover';

export type OwnProps<E extends React.ElementType = React.ElementType> = {
  as?: E;
  active?: boolean;
  className?: string;
  theme?: MenuLinkTheme;
  withMapSync?: boolean;
};

export type Props<E extends React.ElementType> = OwnProps<E> &
  Omit<PropsOf<E>, keyof OwnProps>;

const DefaultElement = 'a';

export const MenuLink = React.forwardRef(
  (
    { as, active, theme = 'default', className, ...restProps }: OwnProps,
    ref: React.Ref<Element>,
  ) => {
    const Element = as || DefaultElement;
    return (
      <Element
        ref={ref}
        className={cx(
          classNames.link,
          classNames[`theme__${theme}`],
          active && classNames.link__active,
          active && 'link__active',
          className,
        )}
        {...restProps}
      />
    );
  },
) as <E extends React.ElementType = typeof DefaultElement>(
  props: Props<E>,
) => JSX.Element;

export default MenuLink;
