import {
  ForwardedRef,
  TextareaHTMLAttributes,
  FocusEvent,
  forwardRef,
  useState,
} from 'react';
import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import IconButton from 'components/IconButton/IconButton';

import classNames from './TextAreaInput.module.css';

export interface Props extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  suffix?: string;
  clearable?: boolean;
  withSearchIcon?: boolean;
  onChangeValue?: (v: string) => void;
  onClear?: () => void;
  error?: string;
}

const TextAreaInput = forwardRef(
  (
    {
      className,
      label,
      suffix,
      value,
      onClear,
      disabled,
      error,
      clearable = false,
      withSearchIcon = false,
      onChangeValue,
      ...otherProps
    }: Props,
    ref: ForwardedRef<HTMLTextAreaElement>,
  ) => {
    const [focused, setFocused] = useState(false);

    const onFocus = (e: FocusEvent<HTMLTextAreaElement>) => {
      otherProps.onFocus?.(e);
      setFocused(true);
    };

    const onBlur = (e: FocusEvent<HTMLTextAreaElement>) => {
      otherProps.onBlur?.(e);
      setFocused(false);
    };

    return (
      <label className={cx(classNames.container, className)}>
        {!!label && <span className={classNames.label}>{label}</span>}
        <div
          className={cx(
            classNames.wrapper,
            focused && classNames.wrapper__focused,
            error && classNames.wrapper__error,
            disabled && classNames.wrapper__disabled,
          )}
        >
          {!!withSearchIcon && (
            <span className={classNames.search}>
              <SvgIcon width={16} height={16} name={'Search'} />
            </span>
          )}

          <textarea
            ref={ref}
            {...otherProps}
            className={classNames.input}
            value={value}
            disabled={disabled}
            onFocus={onFocus}
            onBlur={onBlur}
            onChange={(event) => {
              otherProps.onChange?.(event);
              onChangeValue?.(event.target.value);
            }}
          />

          {!suffix && clearable && !!value && (
            <IconButton
              icon={'Clear'}
              color={'var(--color-grey-50)'}
              hoverColor={'var(--color-grey-100)'}
              onClick={onClear!}
              iconSize={20}
            />
          )}
          {!!suffix && !clearable && (
            <span className={classNames.suffix}>{suffix}</span>
          )}
        </div>
        {!!error && <span className={classNames.error}>{error}</span>}
      </label>
    );
  },
);

export default TextAreaInput;
