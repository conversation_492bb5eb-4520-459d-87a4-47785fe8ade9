.container {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.wrapper {
  border-width: 1px;
  border-style: solid;
  border-color: var(--color-grey-50);
  border-radius: 8px;
  padding: 8px 13px;
  color: var(--color-black);
  transition: color 0.3s, border-color 0.3s;
  display: flex;
  align-items: center;
  gap: 10px;
}

.wrapper:hover {
  cursor: text;
}

.wrapper:disabled:hover {
  cursor: default;
}

.wrapper__error {
  border-color: var(--color-red);
}

.wrapper__focused {
  border-color: var(--color-primary);
}

.wrapper__disabled {
  border-color: var(--color-grey-10);
  color: var(--color-grey-70);
}

.label {
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
}

.search {
  display: flex;
  align-items: center;
  color: var(--color-grey-100);
}

.suffix {
  font-size: 16px;
  line-height: 22px;
  color: var(--color-grey-100);
}

.input {
  border: none;
  padding: 0;
  margin: 0;
  display: block;
  max-width: none;
  width: 100%;
  font-size: 16px;
  line-height: 22px;
  flex-grow: 1;
  background: transparent;
}

.input:focus {
  outline: none;
}
.input::placeholder {
  color: var(--color-grey-100);
}
.input::-moz-placeholder {
  color: var(--color-grey-100);
}
.input::-ms-input-placeholder {
  color: var(--color-grey-100);
}
.input:disabled {
  background: transparent;
  color: var(--color-grey-100);
}

.error {
  margin-top: -2px;
  color: var(--color-red);
  font-size: 14px;
  line-height: 15px;
  font-weight: 500;
}
