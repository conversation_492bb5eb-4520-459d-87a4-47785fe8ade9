import React, { useState } from 'react';

import TextInput from 'components/TextInput/TextInput';
import IconButton from 'components/IconButton/IconButton';

import { Props } from '../TextInput/TextInput';

import classNames from './PasswordInput.module.css';

const PasswordInput = (props: Props) => {
  const [visible, setVisible] = useState(false);

  return (
    <TextInput
      {...props}
      type={visible ? 'text' : 'password'}
      suffix={
        <IconButton
          className={classNames.eyeButton}
          icon={visible ? 'EyeOpen' : 'EyeClose'}
          hoverColor="var(--color-black)"
          iconSize={16}
          hitSlop={23}
          onClick={() => {
            setVisible(!visible);
          }}
        />
      }
    />
  );
};

export default PasswordInput;
