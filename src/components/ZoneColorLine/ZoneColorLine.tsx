import { ReactNode } from 'react';

import classNames from './ZoneColorLine.module.css';

export type Props = {
  color: string;
  title?: ReactNode;
};

const ZoneColorLine = ({ color, title }: Props) => {
  return (
    <div className={classNames.container}>
      <div className={classNames.line} style={{ backgroundColor: color }} />
      {!!title && <div className={classNames.title}>{title}</div>}
    </div>
  );
};

export default ZoneColorLine;
