.trigger {
  height: 42px;
  background-color: var(--color-grey-30);
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px 15px;
  transition: background-color 0.3s;
  outline-offset: -2px;
}

.trigger__error {
  padding: 8px 13px;
  border: 2px solid var(--color-red);
}

.trigger:hover {
  background-color: var(--color-grey-10);
}

.trigger:after {
  flex: 0 0 auto;
  content: '';
  border-left: 3.5px solid transparent;
  border-right: 3.5px solid transparent;
  border-top: 4px solid #222;
  margin-left: 5px;
  width: 0;
  height: 0;
  color: transparent;
  transform: rotate(0deg) translate(0, 0);
  transition: transform 0.3s;
}

.trigger__opened:after {
  transform: rotate(-180deg) translate(0, 1px);
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.triggerValue {
  font-size: 16px;
  line-height: 22px;
  flex: 1 1 auto;
  position: relative;
  min-width: 0;
  user-select: none;
}

.placeholder {
  opacity: 0.5;
}

.label {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.labelValue {
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error {
  margin-top: -2px;
  color: var(--color-red);
  font-size: 14px;
  line-height: 15px;
  font-weight: 500;
}

.popover {
  min-width: 224px;
}
