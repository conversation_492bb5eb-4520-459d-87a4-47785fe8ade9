import { ReactNode } from 'react';

import Dropdown from 'components/Dropdown/Dropdown';
import { DropdownProps } from 'components/Dropdown/types';
import TextInput, {
  Props as TextInputProps,
} from 'components/TextInput/TextInput';

import classNames from './InputWithDropdown.module.css';

export interface Props extends Omit<DropdownProps, 'renderTrigger'> {
  error?: ReactNode;
  disabled?: boolean;
}

type InputWithDropdownProps = TextInputProps & Props;

function InputWithDropdown({
  placeholder,
  error,
  disabled,
  onChangeValue,
  value,
  ...otherProps
}: InputWithDropdownProps) {
  return (
    <Dropdown
      {...otherProps}
      popoverClassName={classNames.popover}
      value={value}
      renderTrigger={({ toggle }) => {
        return (
          <label className={classNames.label}>
            <div className={classNames.triggerValue}>
              <TextInput
                placeholder={placeholder}
                numeric
                positiveOnly
                error={error}
                value={value}
                onClick={(e) => {
                  e.stopPropagation();
                  if (!disabled) {
                    toggle();
                  }
                }}
                onChangeValue={onChangeValue}
              />
            </div>
          </label>
        );
      }}
    />
  );
}
export default InputWithDropdown;
