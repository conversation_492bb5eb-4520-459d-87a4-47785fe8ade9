.radioTile {
  border: 1px solid var(--color-grey-30);
  padding: 1px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
  transition: background-color 0.3s, border-color 0.3s;
  flex: 1 0 188px;
  height: 128px;
  text-align: center;
}

.radioTile:not(.radioTileDisabled):hover {
  background-color: var(--color-grey-10);
}

.radioTileSmall {
  flex: 1 0 120px;
  height: 80px;
}

.radioTileChecked {
  border: 2px solid var(--color-grey-80);
  background-color: var(--color-grey-10);
}

.radioTileDisabled {
  color: var(--color-grey-100);
}

.radioButton {
  position: absolute;
  top: 7px;
  right: 7px;
}
.radioTileChecked .radioButton {
  /* because of border increase */
  top: 6px;
  right: 6px;
}
.radioTileSmall .radioButton,
.radioTileDisabled .radioButton {
  opacity: 0;
}
