import cx from 'classnames';

import RadioButton from 'components/RadioButton';
import SvgIcon from 'components/SvgIcon';
import { Text } from 'components/Text';

import classNames from './RadioTile.module.css';

type RadioTileProps = {
  checked: boolean;
  label: string;
  icon: string;
  onClick: () => void;
  disabled?: boolean;
  small?: boolean;
};

export const RadioTile = ({
  checked,
  label,
  icon,
  onClick,
  disabled,
  small,
}: RadioTileProps) => {
  return (
    <label
      className={cx(
        classNames.radioTile,
        small && classNames.radioTileSmall,
        checked && classNames.radioTileChecked,
        disabled && classNames.radioTileDisabled,
      )}
    >
      <RadioButton
        checked={checked}
        disabled={disabled}
        onSelect={onClick}
        className={classNames.radioButton}
      />
      <SvgIcon name={icon} width={24} height={24} />
      <Text size={small ? 12 : 14} weight={checked ? 'medium' : 'normal'}>
        {label}
      </Text>
    </label>
  );
};
