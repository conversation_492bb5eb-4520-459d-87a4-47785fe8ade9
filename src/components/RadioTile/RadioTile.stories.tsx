import { useState } from 'react';
import { Meta, Story } from '@storybook/react';

import { RadioTile } from 'components/RadioTile';

export default {
  title: 'Components/RadioTile',
  component: RadioTile,
} as Meta;

const wrapperStyle = {
  margin: 20,
  display: 'flex',
  flexDirection: 'column' as const,
  gap: 8,
};
const rowStyle = {
  display: 'flex',
  gap: 8,
};

export const Default: Story = () => {
  const [selected, setSelected] = useState('planting');

  return (
    <div style={wrapperStyle}>
      <div style={rowStyle}>
        <RadioTile
          small
          label="Planting"
          icon="Planting"
          checked={selected === 'planting'}
          onClick={() => setSelected('planting')}
        />
        <RadioTile
          small
          label="Fertiliser application"
          icon="Fertilizing"
          checked={selected === 'fertilizing'}
          onClick={() => setSelected('fertilizing')}
        />
        <RadioTile
          small
          label="Crop protection"
          icon="Protection"
          disabled
          checked={selected === 'protection'}
          onClick={() => setSelected('protection')}
        />
        <RadioTile
          small
          label="Multiple inputs"
          icon="Several"
          disabled
          checked={selected === 'several'}
          onClick={() => setSelected('several')}
        />
      </div>
      <div style={rowStyle}>
        <RadioTile
          label="Planting"
          icon="Planting"
          checked={selected === 'planting'}
          onClick={() => setSelected('planting')}
        />
        <RadioTile
          label="Fertiliser application"
          icon="Fertilizing"
          checked={selected === 'fertilizing'}
          onClick={() => setSelected('fertilizing')}
        />
        <RadioTile
          label="Crop protection"
          icon="Protection"
          disabled
          checked={selected === 'protection'}
          onClick={() => setSelected('protection')}
        />
      </div>
    </div>
  );
};
