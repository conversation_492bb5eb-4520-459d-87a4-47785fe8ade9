import type { XYCoord } from 'react-dnd';
import { useDragLayer } from 'react-dnd';

import classNames from './CustomDragLayer.module.css';

function getItemStyles(
  initialOffset: XYCoord | null,
  currentOffset: XYCoord | null,
) {
  if (!initialOffset || !currentOffset) {
    return {
      display: 'none',
    };
  }

  let { x, y } = currentOffset;

  const transform = `translate(${x}px, ${y}px)`;
  return {
    transform,
    WebkitTransform: transform,
  };
}

export const CustomDragLayer = () => {
  const { isDragging, item, initialOffset, currentOffset } = useDragLayer(
    (monitor) => ({
      item: monitor.getItem(),
      itemType: monitor.getItemType(),
      initialOffset: monitor.getInitialSourceClientOffset(),
      currentOffset: monitor.getSourceClientOffset(),
      isDragging: monitor.isDragging(),
    }),
  );

  if (!isDragging || !item.component) {
    return null;
  }

  const Component = item.component;

  return (
    <div className={classNames.customDragLayer}>
      <div style={getItemStyles(initialOffset, currentOffset)}>
        <Component {...item} />
      </div>
    </div>
  );
};
