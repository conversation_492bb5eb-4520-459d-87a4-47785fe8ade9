import React from 'react';
import ReactDOM from 'react-dom';

export type Props = {
  children: React.ReactNode;
  parent?: Node;
  className?: string;
};

export const Portal = ({ children, parent, className }: Props) => {
  const el = React.useMemo(() => document.createElement('div'), []);

  React.useEffect(() => {
    const target = parent?.appendChild ? parent : document.body;

    const classList = ['portal-container'];
    if (className)
      className.split(' ').forEach((item: string) => classList.push(item));
    classList.forEach((item) => el.classList.add(item));

    target.appendChild(el);
    return () => {
      target.removeChild(el);
    };
  }, [el, parent, className]);

  return ReactDOM.createPortal(children, el);
};

export default Portal;
