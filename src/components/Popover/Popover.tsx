import React, {
  ComponentPropsWithoutRef,
  createContext,
  ReactNode,
  useContext,
  useRef,
} from 'react';
import Tippy from '@tippyjs/react/headless';
import { Instance } from 'tippy.js';
import { useSpring, animated, AnimatedProps } from 'react-spring';
import cx from 'classnames';

import { PopoverProps } from './types';

import classNames from './Popover.module.css';

type PopoverBoxProps = {
  theme?: 'default' | 'dark' | 'transparent';
  style?: AnimatedProps<ComponentPropsWithoutRef<'div'>>['style'];
  mw?: string | number;
  noPadding?: boolean;
  children?: ReactNode;
};

export const DefaultBox = ({
  theme = 'default',
  style,
  mw,
  noPadding,
  children,
}: PopoverBoxProps) => (
  <animated.div
    className={cx(
      classNames.default,
      classNames[`theme__${theme}`],
      noPadding && classNames.noPadding,
    )}
    style={{ ...style, maxWidth: mw }}
  >
    {children}
  </animated.div>
);

export const NavBox = ({ style, mw, children }: PopoverBoxProps) => (
  <animated.div className={classNames.nav} style={{ ...style, maxWidth: mw }}>
    {children}
  </animated.div>
);

export type PopoverContextProps = React.RefObject<Instance> | null;

export const PopoverContext = createContext<PopoverContextProps>(null);

export const usePopover = () => {
  const popoverContext = useContext<PopoverContextProps>(PopoverContext);

  return {
    hide: () => popoverContext?.current?.hide(),
  };
};

const Popover = ({
  children,
  render,
  content,
  maxWidth,
  noPadding = false,
  immediateHide = false,
  theme = 'default',
  onHide: onCustomHide,
  ...tippyProps
}: PopoverProps) => {
  const config = { tension: 300, friction: 15 };
  const initialStyles = {
    opacity: 0,
  };
  const [props, api] = useSpring(() => initialStyles);
  const tippyInstance = useRef<Instance | null>(null);

  const onMount = () => {
    api.start({
      opacity: 1,
      onRest: () => {},
      config,
    });
  };

  const onHide: PopoverProps['onHide'] = (v) => {
    if (onCustomHide) {
      onCustomHide(v);
    }
    api.start({
      ...initialStyles,
      onRest: v.unmount,
      immediate: immediateHide,
      config: { ...config, clamp: true },
    });
  };

  const renderDefaultPopover: PopoverProps['render'] = (attrs) => {
    return (
      <DefaultBox
        style={props}
        mw={maxWidth}
        noPadding={noPadding}
        theme={theme}
        {...attrs}
      >
        {content}
      </DefaultBox>
    );
  };

  return (
    <PopoverContext.Provider value={tippyInstance}>
      <Tippy
        render={render || renderDefaultPopover}
        animation={true}
        onCreate={(instance) => {
          tippyInstance.current = instance;
        }}
        onMount={onMount}
        onHide={onHide}
        maxWidth={maxWidth}
        {...tippyProps}
      >
        {children}
      </Tippy>
    </PopoverContext.Provider>
  );
};

export default Popover;
