import { <PERSON><PERSON>, Story } from '@storybook/react';
import Popover from '../Popover/Popover';
import { PopoverProps } from './types';
import { styled } from '@linaria/react';
import Button from '../Button/Button';

const Wrapper = styled.div`
  margin: 40px auto;
  width: 200px;
  display: flex;
  justify-content: center;
`;

const CustomBox = styled.div`
  padding: 20px;
  background-color: var(--color-grey-10);
  border-top: 5px solid var(--color-primary);
`;

const CustomText = styled.span``;

export const CustomRender: Story<PopoverProps> = (args) => (
  <Wrapper>
    <Popover
      {...args}
      render={(attrs) => {
        return (
          <CustomBox {...attrs}>
            <CustomText>I'm a custom styled popover</CustomText>
          </CustomBox>
        );
      }}
    >
      <Button size={'normal'}>Hover me</Button>
    </Popover>
  </Wrapper>
);

export const ADefaultRender: Story<PopoverProps> = (args) => (
  <Wrapper>
    <Popover {...args}>
      <Button size={'normal'}>Hover me</Button>
    </Popover>
  </Wrapper>
);

export default {
  title: 'Components/Popover',
  argTypes: {
    render: {
      description:
        "Render function for fully controlled popover render. Note that you'll have to implement animations in this case",
      type: 'function',
      table: {
        defaultValue: {
          summary: 'undefined',
        },
      },
    },
    content: {
      description: '',
      type: 'string',
      table: {
        defaultValue: {
          summary: 'React.Node',
        },
      },
    },
    trigger: {
      description:
        'Sets mouse action to show popover. Values can be combined via space: `mouseenter focus`',
      options: ['focus', 'mouseenter', 'click', 'manual'],
      control: {
        type: 'radio',
      },
      table: {
        defaultValue: {
          summary: 'mouseenter focus',
        },
      },
    },
    placement: {
      description:
        'Sets popover placement relative to trigger element. Values `auto`, `auto-start` and `auto-end` automatically choose the side with most space',
      options: [
        'top',
        'top-start',
        'top-end',
        'right',
        'right-start',
        'right-end',
        'bottom',
        'bottom-start',
        'bottom-end',
        'left',
        'left-start',
        'left-end',
        'auto',
        'auto-start',
        'auto-end',
      ],
      control: {
        type: 'select',
      },
      table: {
        defaultValue: {
          summary: 'top',
        },
      },
    },
    interactive: {
      description: 'Sets whether user can or cannot interact with popover',
      type: 'boolean',
      table: {
        defaultValue: {
          summary: false,
        },
      },
    },
    maxWidth: {
      description:
        'Sets popover max width. Can be number for pixels or string for other units: `25%` or `10rem`',
      table: {
        defaultValue: {
          summary: 350,
        },
      },
    },
  },
  args: {
    render: undefined,
    trigger: 'mouseenter',
    placement: 'top',
    interactive: false,
    content: "I'm default styled popover",
    maxWidth: 350,
  },
} as Meta;
