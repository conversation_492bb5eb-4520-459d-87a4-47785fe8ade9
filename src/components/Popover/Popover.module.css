.default {
  padding-top: 8px;
  padding-bottom: 8px;
  box-shadow: 0 0 10px 0 #0000001a;
  border-radius: 8px;
}

.noPadding {
  padding-top: 0;
  padding-bottom: 0;
}

.nav {
  padding-top: 8px;
  padding-bottom: 8px;
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.15),
    0px 0px 2px 0px rgba(0, 0, 0, 0.1);
}

.default.theme__default {
  border: 1px solid var(--color-grey-30);
  background-color: #fff;
}

.default.theme__dark {
  background-color: var(--color-black);
}

.theme__transparent {
  background-color: transparent;
  box-shadow: none;
}
