.container {
  display: flex;
  position: relative;
  width: 100%;
}

.container + .container {
  margin-top: 10px;
}

.widget {
  width: 100%;
}

.widget + .widget {
  margin-left: 10px;
  flex-grow: 0;
  flex-shrink: 0;
}

.limits {
  display: flex;
  justify-content: space-between;
  margin-top: 3px;
}

.limit {
  font-size: 12px;
  line-height: 16px;
}

.label {
  display: block;
  margin-bottom: 3px;
  line-height: 18px;
}

.gradient {
  width: 100%;
  height: 3px;
  border-radius: 3px;
}
