import RangeLegendExample, { Props } from './RangeLegend';
import { styled } from '@linaria/react';
import { <PERSON>a, Story } from '@storybook/react';

export default {
  title: 'Components/RangeLegend',
  component: RangeLegendExample,
  args: {
    colorsRange: ['#CF2F1F', '#E78037', '#FEFB54', '#51B165', '#3C8575'],
    limits: ['Min productivity', 'Max'],
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <RangeLegendExample {...args} />
  </Wrapper>
);
