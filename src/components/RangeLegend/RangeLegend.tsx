import classNames from './RangeLegend.module.css';

export type Props = {
  colorsRange: string[];
  limits: string[];
  label?: string;
};

const RangeLegend = ({ colorsRange, limits, label }: Props) => {
  const background =
    colorsRange.length > 1
      ? `linear-gradient(90deg, ${colorsRange
          .map(
            (color, index) =>
              `${color} ${(index / (colorsRange.length - 1)) * 100}%`,
          )
          .join(', ')})`
      : colorsRange[0] || 'black';

  return (
    <div className={classNames.container}>
      <div className={classNames.widget}>
        {label && <span className={classNames.label}>{label}</span>}
        <div className={classNames.gradient} style={{ background }} />
        <div className={classNames.limits}>
          {limits.map((limit, index) => (
            <span key={index} className={classNames.limit}>
              {limit}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RangeLegend;
