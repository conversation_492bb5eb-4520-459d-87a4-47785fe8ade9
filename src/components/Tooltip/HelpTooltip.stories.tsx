import { HelpTooltip, HelpTooltipProps } from './HelpTooltip';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

export default {
  title: 'Components/HelpTooltip',
  component: HelpTooltip,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  height: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
`;

export const Default: Story<HelpTooltipProps> = () => (
  <Wrapper>
    The text that needs some explanation
    <HelpTooltip>The explanation</HelpTooltip>
  </Wrapper>
);

export const LongText: Story<HelpTooltipProps> = () => (
  <Wrapper>
    The text needs a longer explanation
    <HelpTooltip>
      JavaScript (/ˈdʒɑːvəskrɪpt/),[10] often abbreviated JS, is a programming
      language that is one of the core technologies of the World Wide Web,
      alongside HTML and CSS.[11] Over 97% of websites use JavaScript on the
      client side for web page behavior,[12] often incorporating third-party
      libraries.[13] All major web browsers have a dedicated JavaScript engine
      to execute the code on users' devices.{' '}
    </HelpTooltip>
  </Wrapper>
);
