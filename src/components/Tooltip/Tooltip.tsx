import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import Popover from 'components/Popover/Popover';
import { PopoverProps } from 'components/Popover/types';

import classNames from './Tooltip.module.css';

export type TooltipProps = Omit<PopoverProps, 'render'> & {
  tooltipClassName?: string;
};

export function Tooltip({
  tooltipClassName,
  placement = 'right',
  content,
  ...props
}: TooltipProps) {
  return (
    <Popover
      {...props}
      placement={placement}
      render={(props) => (
        <div
          className={cx(classNames.tooltip, tooltipClassName ?? '')}
          {...props}
        >
          {placement === 'top' ? (
            <div className={classNames.arrow__top} data-popper-arrow="">
              <SvgIcon
                className={classNames.arrow__top}
                name="TooltipArrow"
                width={6}
                height={18}
              />
            </div>
          ) : (
            <div
              className={
                placement === 'bottom'
                  ? classNames.arrow__bottom
                  : classNames.arrow
              }
              data-popper-arrow=""
            >
              <SvgIcon
                className={
                  placement === 'bottom' ? classNames.arrow__bottom : ''
                }
                name="TooltipArrow"
                width={6}
                height={18}
              />
            </div>
          )}
          {content}
        </div>
      )}
    />
  );
}
