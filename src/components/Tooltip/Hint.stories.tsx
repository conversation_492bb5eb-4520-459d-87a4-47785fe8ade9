import { Hint, HintProps } from './Hint';
import { styled } from '@linaria/react';
import { <PERSON>a, <PERSON> } from '@storybook/react';

export default {
  title: 'Components/Hint',
  component: Hint,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  height: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
`;

export const Default: Story<HintProps> = () => (
  <Wrapper>
    The text that needs some&nbsp;
    <Hint content="The explanation">
      <a href="#top">explanation</a>
    </Hint>
  </Wrapper>
);
