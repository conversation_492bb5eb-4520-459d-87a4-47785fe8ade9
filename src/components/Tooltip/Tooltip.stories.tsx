import { Tooltip, TooltipProps } from './Tooltip';
import { styled } from '@linaria/react';
import { <PERSON>a, <PERSON> } from '@storybook/react';

export default {
  title: 'Components/Tooltip',
  component: Tooltip,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  height: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
`;

export const Default: Story<TooltipProps> = () => (
  <Wrapper>
    The text that needs some&nbsp;
    <Tooltip content="The explanation">
      <a href="#top">explanation</a>
    </Tooltip>
  </Wrapper>
);
