import cx from 'classnames';

import SvgIcon, { Props as SvgIconProps } from 'components/SvgIcon';

import classNames from './HelpTooltip.module.css';
import { Tooltip, TooltipProps } from './Tooltip';

export type HelpTooltipProps = {
  className?: string;
  children: TooltipProps['content'];
  icon?: SvgIconProps['name'];
} & Omit<TooltipProps, 'children' | 'content'>;

export function HelpTooltip({
  className,
  children,
  icon = 'Help',
  ...rest
}: HelpTooltipProps) {
  const triggerComponent = (
    <SvgIcon
      className={cx(classNames.icon, className)}
      name={icon}
      width={16}
      height={16}
    />
  );

  return <Tooltip {...rest} content={children} children={triggerComponent} />;
}

export default HelpTooltip;
