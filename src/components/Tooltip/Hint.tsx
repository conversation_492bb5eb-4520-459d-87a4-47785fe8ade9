import { Placement } from 'tippy.js';

import Popover from 'components/Popover/Popover';

import { PopoverProps } from '../Popover/types';
import styles from './Hint.module.css';

export type HintProps = {
  content: string;
  placement?: Placement;
} & PopoverProps;

export function Hint({ trigger, content, placement, ...rest }: HintProps) {
  return (
    <Popover
      {...rest}
      placement={placement ?? 'right'}
      immediateHide={true}
      render={(props) => (
        <div className={styles.hint} {...props}>
          {content}
        </div>
      )}
    />
  );
}
