import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';
import 'mapbox-gl/dist/mapbox-gl.css';
import { useLayoutEffect, useRef } from 'react';
import MapGL, { MapRef } from 'react-map-gl';
import { useStore } from 'effector-react';

import { MapBaseProps } from 'components/MapBase/types';
import { transformRequest } from 'components/MapBase/utils';

import { currentMembership$ } from 'features/multiaccount/models/multiaccount';

import { eventLogged } from 'models/analytics';
import { mapResized } from 'models/map';

import useResizeObserver from 'utils/use-resize-observer';

import config from 'config';

import MapStyle from 'assets/map/style';

import classNames from './MapBase.module.css';

export const MapBase = ({
  onMapResize = mapResized,
  onLoad,
  ...props
}: MapBaseProps) => {
  const sizeMeasuringOverlay = useRef<HTMLDivElement>(null);
  const mapRef = useRef<MapRef | null>(null);
  const mapSize = useResizeObserver(sizeMeasuringOverlay);

  const currentMembership = useStore(currentMembership$);
  const workspaceUuid = currentMembership?.workspace_id;

  useLayoutEffect(() => {
    if (!mapSize) {
      return;
    }
    mapRef.current?.getMap().resize();
    onMapResize && onMapResize(mapSize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mapSize]);

  return (
    <>
      <div ref={sizeMeasuringOverlay} className={classNames.overlay} />
      {!!workspaceUuid && (
        <MapGL
          ref={mapRef}
          mapStyle={MapStyle}
          attributionControl={false}
          mapboxAccessToken={config.apiKeys.mapbox}
          logoPosition="bottom-right"
          transformRequest={transformRequest}
          onLoad={(e) => {
            if (onLoad) {
              onLoad(e);
            }
            eventLogged({
              name: 'yield_map_load',
              params: { url: window.location.href },
            });
          }}
          {...props}
        />
      )}
    </>
  );
};
