import { getApiToken, getWorkspaceUuid } from 'utils/api';
import config from 'config';

export const transformRequest = (url: string, resourceType: string) => {
  if (resourceType === 'Image' && url.includes(config.apiHost)) {
    const apiToken = getApiToken();
    const workspaceUuid = getWorkspaceUuid();

    const headers: Record<string, string> = {};

    if (apiToken) {
      headers['Authorization'] = `Token ${apiToken}`;
    }

    if (workspaceUuid) {
      headers['X-Workspace-ID'] = workspaceUuid;
    }

    return {
      url,
      headers,
    };
  }

  return { url };
};
