.wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  color: var(--color-black);
  transition: color 0.3s;
  cursor: pointer;
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;
  text-align: left;
}

.wrapper:hover {
  color: var(--color-primary);
}

.preview {
  width: 100px;
  height: 75px;
  flex-shrink: 0;
  overflow: hidden;
  border-right: 8px;
}

.preview img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.description {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
}

.title {
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
}

.chip {
  display: flex;
  align-items: center;
  height: 20px;
  padding-left: 9px;
  padding-right: 9px;
  background: var(--color-primary);
  border-radius: 10px;
  gap: 4px;
}

.length {
  font-size: 12px;
  line-height: 12px;
  font-weight: 500;
  color: #ffffff;
}
