import React, { <PERSON><PERSON><PERSON>Handler } from 'react';

import SvgIcon from 'components/SvgIcon';

import classNames from './VideoThumb.module.css';

export type Props = {
  previewUrl: string;
  title: string;
  length: string;
  onClick: MouseEventHandler;
};

const VideoThumb = ({ previewUrl, title, length, onClick }: Props) => {
  return (
    <button onClick={onClick} className={classNames.wrapper}>
      <div className={classNames.preview}>
        <img src={previewUrl} alt={title} />
      </div>
      <div className={classNames.description}>
        <span className={classNames.title}>{title}</span>
        <div className={classNames.chip}>
          <SvgIcon name={'Play'} width={9} height={12} />
          <span className={classNames.length}>{length}</span>
        </div>
      </div>
    </button>
  );
};

export default VideoThumb;
