import VideoThumb, { Props } from './VideoThumb';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';
import VideoPreview from 'assets/video.png';

export default {
  title: 'Components/VideoThumb',
  component: VideoThumb,
  argTypes: {},
  args: {
    previewUrl: VideoPreview,
    title: 'All you want to know about control lines',
    length: '00:51',
    onClick: () => {},
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const Default: Story<Props> = (args) => (
  <Wrapper>
    <VideoThumb {...args} />
  </Wrapper>
);
