import React, { FC, useEffect } from 'react';
import { Source, useMap } from 'react-map-gl';
import { useStore } from 'effector-react';

import { LayerMode, layerMode$, mapLoaded$ } from 'models/map';

type Props = {
  layerMode?: LayerMode;
  isMapLoaded?: boolean;
};

const TerrainLayer: FC<Props> = ({ isMapLoaded, ...props }) => {
  const { current } = useMap();
  const storeLayerMode = useStore(layerMode$);
  const layerMode = props.layerMode || storeLayerMode;
  const storeMapLoaded = useStore(mapLoaded$);
  const mapLoaded = isMapLoaded || storeMapLoaded;

  useEffect(() => {
    if (!mapLoaded) return;
    setTimeout(() => {
      if (layerMode === LayerMode.mode3D) {
        current
          ?.getMap()
          .setTerrain({ source: 'terrain', exaggeration: 2 })
          .easeTo({ pitch: 60 });
      } else {
        current?.getMap().setTerrain(undefined).resetNorthPitch();
      }
    }, 0); // without timeout mapbox cant reset terrain
  }, [current, layerMode, mapLoaded]);

  return (
    <Source
      id="terrain"
      type="raster-dem"
      url="mapbox://mapbox.mapbox-terrain-dem-v1"
      tileSize={512}
    />
  );
};

export default TerrainLayer;
