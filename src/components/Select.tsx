import { ComponentPropsWithoutRef, ForwardedRef, forwardRef } from 'react';
import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';

import classNames from './Select.module.css';

export interface Props extends ComponentPropsWithoutRef<'button'> {
  icon?: string;
  size?: 'normal' | 'small';
  disabled?: boolean;
}

const Select = forwardRef(
  (
    { children, icon, size = 'normal', disabled, ...otherProps }: Props,
    ref: ForwardedRef<HTMLButtonElement>,
  ) => {
    return (
      <button
        className={cx(
          classNames.button,
          classNames['theme__default'],
          classNames[`size__${size}`],
        )}
        disabled={disabled}
        ref={ref}
        {...otherProps}
      >
        {!!icon && <SvgIcon name={icon} width={16} height={16} />}
        {children && <div className={classNames.text}>{children}</div>}
        <SvgIcon name="Expand" width={16} height={16} />
      </button>
    );
  },
);

export default Select;
