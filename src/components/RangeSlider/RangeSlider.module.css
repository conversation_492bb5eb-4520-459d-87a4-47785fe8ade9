.mark {
  width: 2px;
  height: 4px;
  top: 0;
  border-radius: 2px;
  background-color: var(--color-primary);
}

.thumb {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-black);
  background-color: white;
  border-radius: 50%;
  cursor: grab;
  position: absolute;
  top: -9px;
}

.thumb:focus-visible {
  outline: none;
}

.title {
  font-weight: 500;
}

.container {
  margin-left: 15px;
  margin-right: 15px;
  margin-top: 18px;
  margin-bottom: 18px;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 18px;
}

.values {
  color: #5e5e5e;
  flex-shrink: 0;
}

/* Hack to connect styles with react-slider */
.slider :global(.track-slider) {
  height: 2px;
  background: var(--color-grey-30);
}

.slider :global(.track-slider-1) {
  background: var(--color-black);
}
