import { styled } from '@linaria/react';
import { Meta, <PERSON> } from '@storybook/react';
import { useState } from 'react';

import RangeSliderExample, { Props } from './RangeSlider';

export default {
  title: 'Components/3 Sliders/Range Slider',
  component: RangeSliderExample,
  argTypes: {},
  args: {
    min: 10,
    max: 100,
    step: 2,
    unit: '%',
    values: [12, 77],
    title: 'Suitability',
  },
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

export const RangeSlider: Story<Props> = (args) => {
  const [values, setValues] = useState(args.values);
  return (
    <Wrapper>
      <RangeSliderExample
        {...args}
        values={values}
        onChange={(values: [number, number]) => {
          setValues(values);
        }}
      />
    </Wrapper>
  );
};

export const WithMarks: Story<Props> = (args) => {
  const [values, setValues] = useState(args.values);
  return (
    <Wrapper>
      <RangeSliderExample
        {...args}
        marks={[10, 20, 30, 40, 50, 60, 70, 80, 90, 100]}
        marksColors={{ 10: '#FF0000', 40: '#0000FF' }}
        values={values}
        onChange={(values: [number, number]) => {
          setValues(values);
        }}
      />
    </Wrapper>
  );
};

export const Disabled: Story<Props> = (args) => {
  const [values, setValues] = useState<[number, number]>([0, 0]);
  return (
    <Wrapper>
      <RangeSliderExample
        {...args}
        min={0}
        max={0}
        values={values}
        onChange={(values: [number, number]) => {
          setValues(values);
        }}
      />
    </Wrapper>
  );
};
