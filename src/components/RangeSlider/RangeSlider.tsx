import { useRef, useEffect } from 'react';
import ReactSlider from 'react-slider';

import classNames from './RangeSlider.module.css';

export type Props = {
  min: number;
  max: number;
  step: number;
  onChange: (value: [number, number], index: number) => void;
  title?: string;
  values: [number, number];
  marks?: number | boolean | number[];
  marksColors?: { [x: number]: string };
  disabled?: boolean;
  unit: string;
  format?: (value: number, position: 'from' | 'to') => string;
};

const RangeSlider = ({
  min,
  max,
  step,
  onChange,
  title,
  values,
  marks,
  marksColors,
}: Props) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<ReactSlider<[number, number]>>(null);
  const disabled = min === max;

  useEffect(() => {
    if (typeof ResizeObserver === 'undefined') {
      return;
    }
    const cr = containerRef.current;
    const resizeObserver = new ResizeObserver(() => {
      sliderRef.current?.resize();
    });
    resizeObserver.observe(cr!);

    return () => {
      resizeObserver.unobserve(cr!);
    };
  }, []);

  return (
    <div ref={containerRef} className={classNames.container}>
      {title && (
        <div className={classNames.header}>
          <span
            className={classNames.title}
            style={{
              color: disabled ? 'var(--color-grey-100)' : 'inherit',
            }}
          >
            {title}
          </span>
        </div>
      )}
      <ReactSlider
        ref={sliderRef}
        disabled={disabled}
        min={disabled ? 0 : min}
        max={disabled ? 2 : max}
        step={step}
        value={disabled ? [0, 2] : values}
        ariaLabel={['Lower thumb', 'Upper thumb']}
        marks={disabled ? false : marks}
        className={classNames.slider}
        trackClassName="track-slider"
        renderMark={(props) => {
          if (!marksColors) {
            return <span {...props} className={classNames.mark} />;
          }
          const color = marksColors[props.key as number];
          return (
            <span
              {...props}
              className={classNames.mark}
              style={{
                ...props.style,
                backgroundColor: color || 'var(--color-primary)',
              }}
            />
          );
        }}
        renderThumb={(props, state) =>
          state.index === 0 ? (
            <div
              {...props}
              key={'LeftThumb'}
              className={classNames.thumb}
              style={{
                ...props.style,
                transform: state.index ? 'translateX(1px)' : 'translateX(0px)',
              }}
            ></div>
          ) : (
            <div
              {...props}
              key={'RightThumb'}
              className={classNames.thumb}
              style={{
                ...props.style,
                transform: state.index ? 'translateX(1px)' : 'translateX(0px)',
              }}
            ></div>
          )
        }
        onChange={onChange}
      />
    </div>
  );
};

export default RangeSlider;
