import { Outlet } from 'react-router-dom';

import Slot from 'components/Slot';
import MainSidebar from 'components/MainSidebar';
import RenderInSlot from 'components/RenderInSlot';
import { MainSidebarSlot } from 'components/NavigationLayout';

import classNames from './SinglePageLayout.module.css';

export const SinglePageContentSlot = 'single-page-content';

function SinglePageLayout() {
  return (
    <>
      <RenderInSlot id={MainSidebarSlot}>
        <MainSidebar />
      </RenderInSlot>

      <div className={classNames.content}>
        <Slot id={SinglePageContentSlot} exclusive />
      </div>

      {/* We expect this to not render anything directly */}
      <Outlet />
    </>
  );
}

export default SinglePageLayout;
