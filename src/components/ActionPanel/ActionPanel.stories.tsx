import { Meta, <PERSON> } from '@storybook/react';

import { ActionPanel, ActionPanelProps } from './ActionPanel';
import Button from 'components/Button/Button';

export default {
  title: 'Components/ActionPanel',
  component: ActionPanel,
} as Meta;

export const Default: Story<ActionPanelProps> = (args) => (
  <div
    style={{
      width: 'fit-content',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 20,
      margin: 20,
    }}
  >
    <ActionPanel>
      <Button size="normal" icon="Delete" theme="black">
        Delete
      </Button>
      <Button size="normal" icon="Folder" theme="black" disabled>
        Move to
      </Button>
      <Button size="normal" icon="Kebab" theme="black" />
      <Button size="normal" icon="Cross" theme="black" />
    </ActionPanel>
  </div>
);
