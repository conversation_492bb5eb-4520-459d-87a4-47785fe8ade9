import { useState, useEffect } from 'react';

import type { BBLeapWorkspace } from 'features/vra/models/bbleap/types';

interface UseMachineSelectionProps {
  selectedWorkspace: BBLeapWorkspace | undefined;
  selectedMachineIds: string[];
  onMachineSelectionChange: (machineIds: string[]) => void;
}

export const useMachineSelection = ({
  selectedWorkspace,
  selectedMachineIds,
  onMachineSelectionChange,
}: UseMachineSelectionProps) => {
  const [filter, setFilter] = useState('');
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  const machines = selectedWorkspace?.bb_leap_machines || [];
  const filteredMachines = machines.filter((m) =>
    m.nickname.toLowerCase().includes(filter.toLowerCase()),
  );

  useEffect(() => {
    const currentMachines = selectedWorkspace?.bb_leap_machines || [];
    if (
      currentMachines.length === 1 &&
      !hasUserInteracted &&
      currentMachines[0]
    ) {
      onMachineSelectionChange([currentMachines[0].external_id]);
    }
  }, [
    selectedWorkspace?.bb_leap_machines,
    selectedWorkspace?.external_id,
    hasUserInteracted,
    onMachineSelectionChange,
  ]);

  useEffect(() => {
    setHasUserInteracted(false);
  }, [selectedWorkspace?.external_id]);

  const handleCheckboxChange = (checked: boolean, machineId: string) => {
    setHasUserInteracted(true);
    let newValue: string[];

    if (checked) {
      newValue = [...selectedMachineIds, machineId];
    } else {
      newValue = selectedMachineIds.filter((id) => id !== machineId);
    }

    onMachineSelectionChange(newValue);
  };

  const areAllFilteredMachinesSelected = () => {
    if (filteredMachines.length === 0) return false;
    return filteredMachines.every((machine) =>
      selectedMachineIds.includes(machine.external_id),
    );
  };

  const handleSelectAllToggle = () => {
    setHasUserInteracted(true);
    const allSelected = areAllFilteredMachinesSelected();

    if (allSelected) {
      // Note: Deselect all filtered machines
      const filteredMachineIds = filteredMachines.map((m) => m.external_id);
      const newValue = selectedMachineIds.filter(
        (id) => !filteredMachineIds.includes(id),
      );
      onMachineSelectionChange(newValue);
    } else {
      // Note: Select all filtered machines
      const filteredMachineIds = filteredMachines.map((m) => m.external_id);
      const newValue = [
        ...new Set([...selectedMachineIds, ...filteredMachineIds]),
      ];
      onMachineSelectionChange(newValue);
    }
  };

  return {
    filter,
    setFilter,
    machines,
    filteredMachines,
    handleCheckboxChange,
    areAllFilteredMachinesSelected,
    handleSelectAllToggle,
  };
};
