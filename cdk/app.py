#!/usr/bin/env python3
import os

import aws_cdk as cdk

from cdk.static_s3_site import StaticS3Site
from common.get_configuration import AppConfiguration

current_branch = os.getenv('ENV_NAME')[:35] # limit on the name of S3 bucket
project_name = os.getenv('PROJECT_NAME')

app = cdk.App()
config = AppConfiguration.get_configuration(current_branch, project_name)

StaticS3Site(app, config["stack_name"], env=cdk.Environment(
    account=os.environ["CDK_DEFAULT_ACCOUNT"],
    region=os.environ["CDK_DEFAULT_REGION"]),
    config=config)

app.synth()
