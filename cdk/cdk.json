{"app": "python3 app.py", "watch": {"include": ["**"], "exclude": ["README.md", "cdk*.json", "requirements*.txt", "source.bat", "**/__init__.py", "python/__pycache__", "tests"]}, "context": {"@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId": true, "@aws-cdk/core:stackRelativeExports": true, "@aws-cdk/aws-rds:lowercaseDbIdentifier": true, "@aws-cdk/aws-lambda:recognizeVersionProps": true, "@aws-cdk/aws-lambda:recognizeLayerVersion": true, "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021": true, "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": true, "@aws-cdk/core:validateSnapshotRemovalPolicy": true, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": true, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": true, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": true, "@aws-cdk/core:target-partitions": ["aws", "aws-cn"]}}