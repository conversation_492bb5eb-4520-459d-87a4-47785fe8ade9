from datetime import datetime
import aws_cdk as cdk

class AppConfiguration:
    def get_configuration(branch, project_name):
        config = {}

        if branch == "main":
            config["bucket_name"] = "static-correct-crow"
            config["certificate_arn"] = "arn:aws:acm:us-east-1:275509970230:certificate/50f1fe1c-6499-4f60-9264-204f0ae3cccb"
            config["aliases"] = ["app.yield.onesoil.ai"]
            config["hosted_zone"] = ["prod-fe.onesoil.ai"]
            config["stack_name"] = "yield-production"
        elif branch == "develop":
            config["bucket_name"] = "static-proud-ocelot"
            config["certificate_arn"] = "arn:aws:acm:us-east-1:398868223311:certificate/14e98d48-85f5-4a20-90d7-5f3ee6e472b9"
            config["aliases"] = ["yield.dev-fe.onesoil.ai"]
            config["hosted_zone"] = ["dev-fe.onesoil.ai"]
            config["stack_name"] = "yield-develop"
        elif branch.startswith("release"): # staging for release branches
            config["bucket_name"] = "yield.stg-fe.onesoil.ai"
            config["certificate_arn"] = "arn:aws:acm:us-east-1:398868223311:certificate/3b12ddf8-cb0b-4b7f-a955-d059f53a7e08"
            config["aliases"] = ["yield.stg-fe.onesoil.ai"]
            config["hosted_zone"] = ["stg-fe.onesoil.ai"]
            config["stack_name"] = "yield-staging"
        else: # feature envs
            config["bucket_name"] = f"{project_name}-{branch}.dev-fe.onesoil.ai"
            config["certificate_arn"] = "arn:aws:acm:us-east-1:398868223311:certificate/14e98d48-85f5-4a20-90d7-5f3ee6e472b9"
            config["aliases"] = [config["bucket_name"]]
            config["hosted_zone"] = ["dev-fe.onesoil.ai"]
            config["stack_name"] = f"{project_name}-{branch}"

        return config
