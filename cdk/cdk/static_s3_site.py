from aws_cdk import Stack
from aws_cdk import RemovalP<PERSON>y
from aws_cdk import aws_iam as iam
from aws_cdk import aws_s3 as s3
from aws_cdk import aws_route53 as route53
from aws_cdk import aws_route53_targets as targets
from aws_cdk import aws_cloudfront as cloudfront
from aws_cdk import aws_certificatemanager as certificatemanager
from aws_cdk import Tags as tags
import aws_cdk as cdk
from constructs import Construct

class StaticS3Site(Stack):

    def __init__(self, scope: Construct, stack_name: str, config: dict, **kwargs) -> None:
        super().__init__(scope, stack_name, **kwargs)

        bucket = s3.Bucket(self,"bucket", bucket_name=config["bucket_name"],
                            removal_policy=RemovalPolicy.DESTROY,
                            auto_delete_objects=True,
                            website_index_document="index.html",
                            website_error_document="index.html",
                            object_ownership=s3.ObjectOwnership.BUCKET_OWNER_ENFORCED)

        domain_cert = certificatemanager.Certificate.from_certificate_arn(self, "*.stg-fe.onesoil.ai", config["certificate_arn"])

        viewer_certificate = cloudfront.ViewerCertificate.from_acm_certificate(domain_cert,
            aliases=config["aliases"],
            security_policy=cloudfront.SecurityPolicyProtocol.TLS_V1,  # default
            ssl_method=cloudfront.SSLMethod.SNI
        )

        origin_access_identity = cloudfront.OriginAccessIdentity(self, "SpecialUser",
            comment=f"Identity for {config['aliases'][0]}"
        )

        cloudfront_dist = cloudfront.CloudFrontWebDistribution(self, "MyDistribution",
            origin_configs=[cloudfront.SourceConfiguration(
                s3_origin_source=cloudfront.S3OriginConfig(
                    s3_bucket_source=bucket,
                    origin_access_identity=origin_access_identity
                ),
                behaviors=[cloudfront.Behavior(is_default_behavior=True)]
            )
            ],
            error_configurations=[
                cloudfront.CfnDistribution.CustomErrorResponseProperty(
                    error_code=403,
                    response_code=200,
                    response_page_path="/index.html"
                ),
                cloudfront.CfnDistribution.CustomErrorResponseProperty(
                    error_code=404,
                    response_code=200,
                    response_page_path="/index.html"
                )
            ],
            viewer_certificate=viewer_certificate
        )

        zone = route53.PublicHostedZone.from_lookup(self,"zone",
                            domain_name=config["hosted_zone"][0])

        route53.ARecord(self, "alias",
                zone=zone,
                record_name=config["aliases"][0],
                target=route53.RecordTarget.from_alias(targets.CloudFrontTarget(cloudfront_dist))
        )

        tags.of(cloudfront_dist).add("Contact","Operations")
        tags.of(cloudfront_dist).add("Department","Operations")
        tags.of(cloudfront_dist).add("Environment","Development")
        tags.of(cloudfront_dist).add("ManagedBy","Terraform")

        cdk.CfnOutput(self, "CloudfrontId", value=cloudfront_dist.distribution_id)
