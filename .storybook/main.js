const macrosPlugin = require('vite-plugin-babel-macros').default;
const { createSvgIconsPlugin } = require('vite-plugin-svg-icons');
const tsconfigPaths = require('vite-tsconfig-paths').default;
const linaria = require('@linaria/rollup').default;
const path = require('path');

module.exports = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: ['@storybook/addon-links', '@storybook/addon-essentials'],
  staticDirs: ['../public'],
  framework: '@storybook/react',
  core: {
    builder: '@storybook/builder-vite',
  },
  async viteFinal(config, { configType }) {
    // customize the Vite config here
    config = {
      ...config,
      plugins: [
        ...config.plugins,
        macrosPlugin(),
        createSvgIconsPlugin({
          // Specify the icon folder to be cached
          iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
          // Specify symbolId format
          symbolId: 'icon-[dir]-[name]',
        }),
        tsconfigPaths(),
        linaria({
          exclude: /node_modules/,
          sourceMap: false,
        }),
      ],
      optimizeDeps: {
        entries: [
          `${path.relative(
            config.root,
            path.resolve(__dirname, '../src'),
          )}/**/*.stories.@(js|jsx|ts|tsx)`,
        ],
        include: [
          ...(config.optimizeDeps?.include ?? []),
          '@storybook/react',
          '@storybook/client-api',
          '@storybook/client-logger',
          '@turf/bbox-polygon',
          'effector-storage/local',
          'effector-storage/query',
          'i18next',
          'i18next-browser-languagedetector',
          'lodash/keyBy',
          'lodash/mapValues',
          'lodash/sample',
          'lodash/sampleSize',
          'patronum',
          'patronum/debounce',
          'viewport-mercator-project',
        ],
      },
      build: {
        ...config.build,
        sourcemap: configType !== 'PRODUCTION',
      },
    };

    // return the customized config
    return config;
  },
  typescript: {
    check: false,
    checkOptions: {},
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      shouldRemoveUndefinedFromOptional: true,
      propFilter: (prop) =>
        prop.parent ? !/node_modules/.test(prop.parent.fileName) : true,
    },
  },
};
