import 'virtual:svg-icons-register';
import { MemoryRouter } from 'react-router-dom';
import { action } from '@storybook/addon-actions';
import { rest } from 'msw';
import { initialize, mswDecorator } from 'msw-storybook-addon';
import { useEffect } from 'react';

import 'styles/global.css';
import 'styles/variables.css';
import 'utils/i18n';

import { AppLoadGate } from 'models/app';
import { makeApiUrl, makeMockResponse } from 'utils/api';

import {
  seasonFirst,
  fieldSeasonFirst,
  reportFirst,
  processingStatusFirst,
  sowingVRAMap,
} from './mocks';

// Initialize MSW
initialize({
  quiet: true,
  onUnhandledRequest: 'bypass',
});

export const decorators = [
  mswDecorator,
  (Story, options) => {
    const { stores = [], location = '/', useAppLoadGate } = options.parameters;

    useEffect(() => {
      // Set initial stores state
      for (const { store, value } of stores) {
        store.setState(value);
      }
      return () => {
        // Reset stores after unmount / story change
        for (const { store } of stores) {
          store.setState(store.defaultState);
        }
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
      <MemoryRouter initialEntries={[location]}>
        {useAppLoadGate && <AppLoadGate navigate={action('navigate')} />}
        <Story />
      </MemoryRouter>
    );
  },
];

export const parameters = {
  actions: { argTypesRegex: '^on[A-Z].*' },
  options: {
    storySort: {
      method: 'alphabetical',
      includeName: true,
      locales: 'en-US',
    },
  },
  controls: {
    expanded: true,
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
  msw: {
    handlers: {
      seasons: [
        rest.get(makeApiUrl('seasons'), makeMockResponse([seasonFirst])),
      ],
      fieldSeasons: [
        rest.get(
          makeApiUrl('fields-users-seasons'),
          makeMockResponse([fieldSeasonFirst]),
        ),
      ],
      fieldSeason: [
        rest.get(
          makeApiUrl('fields-users-seasons'),
          makeMockResponse([
            { ...fieldSeasonFirst, sowing_vra_maps: [sowingVRAMap] },
          ]),
        ),
      ],
      processingStatusesCalculate: [
        rest.post(
          makeApiUrl('fields-users-seasons-productivity-maps/calculate'),
          makeMockResponse({ count: 1 }),
        ),
      ],
      processingStatusesFetch: [
        rest.get(
          makeApiUrl('fields-users-seasons-productivity-maps'),
          makeMockResponse([processingStatusFirst]),
        ),
      ],
      report: rest.get(
        makeApiUrl(
          `fields-users-seasons-productivity-maps/${fieldSeasonFirst.id}/report`,
        ),
        makeMockResponse(reportFirst),
      ),
    },
  },
};
