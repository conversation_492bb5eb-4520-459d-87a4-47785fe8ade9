import { Season } from '../src/models/seasons';
import { FieldSeason, FieldSeasonSource } from '../src/models/fieldSeasons';
import { ProcessingStatus } from '../src/models/processingStatuses';
import { Report } from '../src/features/field-page/models/report';
import { ProductivityZone, SowingVRAMap } from 'models/vraMaps';

export const seasonFirst: Season = {
  id: 91733,
  uuid: 'cef59338-567b-4fb3-afbf-78d33c5e5207',
  title: null,
  start_date: '2022-01-01',
  end_date: '2022-12-31',
  created_at: '2022-02-21T08:17:58.998554Z',
  updated_at: '2022-02-21T08:17:58.998567Z',
  is_deleted: false,
};

export const fieldSeasonFirst: FieldSeason = {
  active_pa_info_uuid: null,
  id: 991186,
  uuid: '9401b568-b74a-4a58-ab85-fbda698445cc',
  field_user_id: 767415,
  field_user: {
    id: 767415,
    uuid: 'b0fefe1f-daa1-4dd7-96b7-857248689fd1',
    title: 'Field 1',
    index_number: 1,
    created_at: '2022-02-21T08:53:26.869562Z',
    updated_at: '2022-02-21T08:53:26.869581Z',
  },
  season_id: 91733,
  crops: [
    {
      id: 207383,
      uuid: '77397f02-8013-4a2b-b51d-74ef121c024d',
      field_user_season_id: 991186,
      sowing_date: null,
      harvest_date: null,
      color: '#000000',
      crop: 'almond',
      variety: null,
      yield_value: null,
      created_at: '2022-02-21T08:53:27.122400Z',
      updated_at: '2022-02-21T08:53:27.122417Z',
      is_deleted: false,
      source: 'when_add_field',
    },
  ],
  source: FieldSeasonSource.PREDICTED,
  is_deleted: false,
  area: 84.18927050784976,
  created_at: '2022-02-21T08:53:27.101182Z',
  updated_at: '2022-02-21T08:53:27.101205Z',
  bbox: [-62.96500278, -33.498644717, -62.95015773, -33.490395657],
  centroid: {
    type: 'Point',
    coordinates: [-62.95749576561474, -33.49447531730223],
  },
  geom: {
    type: 'MultiPolygon',
    coordinates: [
      [
        [
          [-62.96262468, -33.490512709],
          [-62.962592371, -33.490530743],
          [-62.962538533, -33.490530727],
          [-62.962323251, -33.490530661],
          [-62.961677285, -33.490710857],
          [-62.961085201, -33.490755771],
          [-62.960170074, -33.490958424],
          [-62.959254993, -33.491161067],
          [-62.957532505, -33.49138598],
          [-62.956886522, -33.491566151],
          [-62.956294429, -33.491611042],
          [-62.955002499, -33.491881169],
          [-62.954679551, -33.491881051],
          [-62.95451803, -33.491971189],
          [-62.954195081, -33.49197107],
          [-62.954033559, -33.492061207],
          [-62.953279962, -33.492151122],
          [-62.95284929, -33.492286253],
          [-62.951346233, -33.49247647],
          [-62.951072938, -33.492511056],
          [-62.950922207, -33.492565111],
          [-62.950696085, -33.492646202],
          [-62.950534606, -33.492646137],
          [-62.950373134, -33.492646073],
          [-62.950319283, -33.49269115],
          [-62.95015773, -33.49282638],
          [-62.950211168, -33.493502876],
          [-62.950372439, -33.493863727],
          [-62.950425983, -33.494359829],
          [-62.950640981, -33.494901095],
          [-62.950748276, -33.495532514],
          [-62.950855854, -33.495667851],
          [-62.950855625, -33.496073736],
          [-62.951339188, -33.497652366],
          [-62.951339011, -33.497968054],
          [-62.951446593, -33.498103391],
          [-62.95150022, -33.498464198],
          [-62.951715435, -33.498644675],
          [-62.951769264, -33.498644696],
          [-62.951823093, -33.498644717],
          [-62.951876972, -33.498554541],
          [-62.952146166, -33.498464449],
          [-62.952845989, -33.498374521],
          [-62.953012939, -33.498322141],
          [-62.953276692, -33.498239389],
          [-62.953693073, -33.498210476],
          [-62.95392266, -33.498194533],
          [-62.954156034, -33.498134466],
          [-62.954622527, -33.498014398],
          [-62.955160835, -33.497969496],
          [-62.957206541, -33.497519237],
          [-62.957973978, -33.497488881],
          [-62.958280436, -33.497476759],
          [-62.958336956, -33.497474523],
          [-62.958390827, -33.497384345],
          [-62.958749709, -33.497324334],
          [-62.958929151, -33.497294328],
          [-62.95903685, -33.497204167],
          [-62.959575151, -33.497159246],
          [-62.961943801, -33.496663915],
          [-62.96307424, -33.496528962],
          [-62.963289589, -33.496438829],
          [-62.963760636, -33.49636004],
          [-62.96463535, -33.496213731],
          [-62.964904525, -33.496168708],
          [-62.96500278, -33.496004205],
          [-62.96492597, -33.495722924],
          [-62.964689692, -33.494996091],
          [-62.964689748, -33.494857868],
          [-62.964597134, -33.494518711],
          [-62.96458224, -33.494499979],
          [-62.964570535, -33.494421304],
          [-62.964509337, -33.494197195],
          [-62.964370542, -33.493688923],
          [-62.964219424, -33.493135521],
          [-62.964206037, -33.493101833],
          [-62.964130752, -33.492912381],
          [-62.963937184, -33.492425271],
          [-62.963453516, -33.490621197],
          [-62.963292139, -33.490395657],
          [-62.963112716, -33.490410637],
          [-62.962753881, -33.490440595],
          [-62.96262468, -33.490512709],
        ],
      ],
    ],
  },
  geom_updated_at: null,
  country_id: null,
  active_pa_info: null,
  sharing_code: null,
};

export const processingStatusFirst: ProcessingStatus = {
  algo_status_code: null,
  algo_successful_execution: null,
  algo_version: null,
  created_at: '',
  updated_at: '',
  uuid: '',
  status: 'finished',
  suitability_status: 'suitable',
  suitability: 0.95,
  progress: 1,
  estimate: 0,
  field_user_season_uuid: fieldSeasonFirst.uuid,
};
export const processingStatusProcessing: ProcessingStatus = {
  algo_status_code: null,
  algo_successful_execution: null,
  algo_version: null,
  created_at: '',
  updated_at: '',
  uuid: '',
  status: 'processing',
  suitability_status: 'suitable',
  suitability: null,
  progress: 0,
  estimate: 14400,
  field_user_season_uuid: fieldSeasonFirst.uuid,
};

export const reportFirst: Report = {
  ndvi_scenes: [
    {
      url: 'https://placekitten.com/180/180',
      date: '2018-01-01',
      uuid: 'f64232de-09c8-477a-942e-c7556416eb74',
      enabled: false,
    },
  ],
  productivity_map: 'https://placekitten.com/300/300',
  brightness_map: 'https://placekitten.com/300/300',
  elevation_map: 'https://placekitten.com/300/300',
  palettes: {
    ndvi_scene: { labels: [] },
    productivity_map: { labels: [] },
    brightness_map: { labels: [] },
    elevation_map: { labels: [] },
  },
};

export const sowingVRAMap: SowingVRAMap = {
  control_lines: null,
  control_lines_uuid: null,
  overlaid_control_lines: null,
  productivity_zone_uuid: '',
  uuid: 'f64232de-09c8-477a-942e-c7556416eb74',
  pa_info_uuid: '6f0ba4f9-2a28-477c-a58c-64ec7d83d730',
  crop: 'almond',
  variety: null,
  average_rate: 6,
  rates_unit: 'seeds/ac',
  unit_system: 'imperial',
  rates: { m1: 1, p0: 2, p1: 3 },
  is_deleted: false,
  created_at: '2022-02-28T06:05:23.585649Z',
  updated_at: '2022-02-28T06:05:23.585668Z',
  productivity_zone: {} as ProductivityZone,
};
